{"ast": null, "code": "import capitalize from '@mui/utils/capitalize';\nimport responsivePropType from \"../responsivePropType/index.js\";\nimport { handleBreakpoints } from \"../breakpoints/index.js\";\nexport function getPath(obj, path, checkVars = true) {\n  if (!path || typeof path !== 'string') {\n    return null;\n  }\n\n  // Check if CSS variables are used\n  if (obj && obj.vars && checkVars) {\n    const val = `vars.${path}`.split('.').reduce((acc, item) => acc && acc[item] ? acc[item] : null, obj);\n    if (val != null) {\n      return val;\n    }\n  }\n  return path.split('.').reduce((acc, item) => {\n    if (acc && acc[item] != null) {\n      return acc[item];\n    }\n    return null;\n  }, obj);\n}\nexport function getStyleValue(themeMapping, transform, propValueFinal, userValue = propValueFinal) {\n  let value;\n  if (typeof themeMapping === 'function') {\n    value = themeMapping(propValueFinal);\n  } else if (Array.isArray(themeMapping)) {\n    value = themeMapping[propValueFinal] || userValue;\n  } else {\n    value = getPath(themeMapping, propValueFinal) || userValue;\n  }\n  if (transform) {\n    value = transform(value, userValue, themeMapping);\n  }\n  return value;\n}\nfunction style(options) {\n  const {\n    prop,\n    cssProperty = options.prop,\n    themeKey,\n    transform\n  } = options;\n\n  // false positive\n  // eslint-disable-next-line react/function-component-definition\n  const fn = props => {\n    if (props[prop] == null) {\n      return null;\n    }\n    const propValue = props[prop];\n    const theme = props.theme;\n    const themeMapping = getPath(theme, themeKey) || {};\n    const styleFromPropValue = propValueFinal => {\n      let value = getStyleValue(themeMapping, transform, propValueFinal);\n      if (propValueFinal === value && typeof propValueFinal === 'string') {\n        // Haven't found value\n        value = getStyleValue(themeMapping, transform, `${prop}${propValueFinal === 'default' ? '' : capitalize(propValueFinal)}`, propValueFinal);\n      }\n      if (cssProperty === false) {\n        return value;\n      }\n      return {\n        [cssProperty]: value\n      };\n    };\n    return handleBreakpoints(props, propValue, styleFromPropValue);\n  };\n  fn.propTypes = process.env.NODE_ENV !== 'production' ? {\n    [prop]: responsivePropType\n  } : {};\n  fn.filterProps = [prop];\n  return fn;\n}\nexport default style;", "map": {"version": 3, "names": ["capitalize", "responsivePropType", "handleBreakpoints", "<PERSON><PERSON><PERSON>", "obj", "path", "checkVars", "vars", "val", "split", "reduce", "acc", "item", "getStyleValue", "themeMapping", "transform", "propValueFinal", "userValue", "value", "Array", "isArray", "style", "options", "prop", "cssProperty", "<PERSON><PERSON><PERSON>", "fn", "props", "propValue", "theme", "styleFromPropValue", "propTypes", "process", "env", "NODE_ENV", "filterProps"], "sources": ["D:/HeThongCongTyQuanLyNhanCong/Microservice_With_Kubernetes/microservice_fe/node_modules/@mui/system/esm/style/style.js"], "sourcesContent": ["import capitalize from '@mui/utils/capitalize';\nimport responsivePropType from \"../responsivePropType/index.js\";\nimport { handleBreakpoints } from \"../breakpoints/index.js\";\nexport function getPath(obj, path, checkVars = true) {\n  if (!path || typeof path !== 'string') {\n    return null;\n  }\n\n  // Check if CSS variables are used\n  if (obj && obj.vars && checkVars) {\n    const val = `vars.${path}`.split('.').reduce((acc, item) => acc && acc[item] ? acc[item] : null, obj);\n    if (val != null) {\n      return val;\n    }\n  }\n  return path.split('.').reduce((acc, item) => {\n    if (acc && acc[item] != null) {\n      return acc[item];\n    }\n    return null;\n  }, obj);\n}\nexport function getStyleValue(themeMapping, transform, propValueFinal, userValue = propValueFinal) {\n  let value;\n  if (typeof themeMapping === 'function') {\n    value = themeMapping(propValueFinal);\n  } else if (Array.isArray(themeMapping)) {\n    value = themeMapping[propValueFinal] || userValue;\n  } else {\n    value = getPath(themeMapping, propValueFinal) || userValue;\n  }\n  if (transform) {\n    value = transform(value, userValue, themeMapping);\n  }\n  return value;\n}\nfunction style(options) {\n  const {\n    prop,\n    cssProperty = options.prop,\n    themeKey,\n    transform\n  } = options;\n\n  // false positive\n  // eslint-disable-next-line react/function-component-definition\n  const fn = props => {\n    if (props[prop] == null) {\n      return null;\n    }\n    const propValue = props[prop];\n    const theme = props.theme;\n    const themeMapping = getPath(theme, themeKey) || {};\n    const styleFromPropValue = propValueFinal => {\n      let value = getStyleValue(themeMapping, transform, propValueFinal);\n      if (propValueFinal === value && typeof propValueFinal === 'string') {\n        // Haven't found value\n        value = getStyleValue(themeMapping, transform, `${prop}${propValueFinal === 'default' ? '' : capitalize(propValueFinal)}`, propValueFinal);\n      }\n      if (cssProperty === false) {\n        return value;\n      }\n      return {\n        [cssProperty]: value\n      };\n    };\n    return handleBreakpoints(props, propValue, styleFromPropValue);\n  };\n  fn.propTypes = process.env.NODE_ENV !== 'production' ? {\n    [prop]: responsivePropType\n  } : {};\n  fn.filterProps = [prop];\n  return fn;\n}\nexport default style;"], "mappings": "AAAA,OAAOA,UAAU,MAAM,uBAAuB;AAC9C,OAAOC,kBAAkB,MAAM,gCAAgC;AAC/D,SAASC,iBAAiB,QAAQ,yBAAyB;AAC3D,OAAO,SAASC,OAAOA,CAACC,GAAG,EAAEC,IAAI,EAAEC,SAAS,GAAG,IAAI,EAAE;EACnD,IAAI,CAACD,IAAI,IAAI,OAAOA,IAAI,KAAK,QAAQ,EAAE;IACrC,OAAO,IAAI;EACb;;EAEA;EACA,IAAID,GAAG,IAAIA,GAAG,CAACG,IAAI,IAAID,SAAS,EAAE;IAChC,MAAME,GAAG,GAAG,QAAQH,IAAI,EAAE,CAACI,KAAK,CAAC,GAAG,CAAC,CAACC,MAAM,CAAC,CAACC,GAAG,EAAEC,IAAI,KAAKD,GAAG,IAAIA,GAAG,CAACC,IAAI,CAAC,GAAGD,GAAG,CAACC,IAAI,CAAC,GAAG,IAAI,EAAER,GAAG,CAAC;IACrG,IAAII,GAAG,IAAI,IAAI,EAAE;MACf,OAAOA,GAAG;IACZ;EACF;EACA,OAAOH,IAAI,CAACI,KAAK,CAAC,GAAG,CAAC,CAACC,MAAM,CAAC,CAACC,GAAG,EAAEC,IAAI,KAAK;IAC3C,IAAID,GAAG,IAAIA,GAAG,CAACC,IAAI,CAAC,IAAI,IAAI,EAAE;MAC5B,OAAOD,GAAG,CAACC,IAAI,CAAC;IAClB;IACA,OAAO,IAAI;EACb,CAAC,EAAER,GAAG,CAAC;AACT;AACA,OAAO,SAASS,aAAaA,CAACC,YAAY,EAAEC,SAAS,EAAEC,cAAc,EAAEC,SAAS,GAAGD,cAAc,EAAE;EACjG,IAAIE,KAAK;EACT,IAAI,OAAOJ,YAAY,KAAK,UAAU,EAAE;IACtCI,KAAK,GAAGJ,YAAY,CAACE,cAAc,CAAC;EACtC,CAAC,MAAM,IAAIG,KAAK,CAACC,OAAO,CAACN,YAAY,CAAC,EAAE;IACtCI,KAAK,GAAGJ,YAAY,CAACE,cAAc,CAAC,IAAIC,SAAS;EACnD,CAAC,MAAM;IACLC,KAAK,GAAGf,OAAO,CAACW,YAAY,EAAEE,cAAc,CAAC,IAAIC,SAAS;EAC5D;EACA,IAAIF,SAAS,EAAE;IACbG,KAAK,GAAGH,SAAS,CAACG,KAAK,EAAED,SAAS,EAAEH,YAAY,CAAC;EACnD;EACA,OAAOI,KAAK;AACd;AACA,SAASG,KAAKA,CAACC,OAAO,EAAE;EACtB,MAAM;IACJC,IAAI;IACJC,WAAW,GAAGF,OAAO,CAACC,IAAI;IAC1BE,QAAQ;IACRV;EACF,CAAC,GAAGO,OAAO;;EAEX;EACA;EACA,MAAMI,EAAE,GAAGC,KAAK,IAAI;IAClB,IAAIA,KAAK,CAACJ,IAAI,CAAC,IAAI,IAAI,EAAE;MACvB,OAAO,IAAI;IACb;IACA,MAAMK,SAAS,GAAGD,KAAK,CAACJ,IAAI,CAAC;IAC7B,MAAMM,KAAK,GAAGF,KAAK,CAACE,KAAK;IACzB,MAAMf,YAAY,GAAGX,OAAO,CAAC0B,KAAK,EAAEJ,QAAQ,CAAC,IAAI,CAAC,CAAC;IACnD,MAAMK,kBAAkB,GAAGd,cAAc,IAAI;MAC3C,IAAIE,KAAK,GAAGL,aAAa,CAACC,YAAY,EAAEC,SAAS,EAAEC,cAAc,CAAC;MAClE,IAAIA,cAAc,KAAKE,KAAK,IAAI,OAAOF,cAAc,KAAK,QAAQ,EAAE;QAClE;QACAE,KAAK,GAAGL,aAAa,CAACC,YAAY,EAAEC,SAAS,EAAE,GAAGQ,IAAI,GAAGP,cAAc,KAAK,SAAS,GAAG,EAAE,GAAGhB,UAAU,CAACgB,cAAc,CAAC,EAAE,EAAEA,cAAc,CAAC;MAC5I;MACA,IAAIQ,WAAW,KAAK,KAAK,EAAE;QACzB,OAAON,KAAK;MACd;MACA,OAAO;QACL,CAACM,WAAW,GAAGN;MACjB,CAAC;IACH,CAAC;IACD,OAAOhB,iBAAiB,CAACyB,KAAK,EAAEC,SAAS,EAAEE,kBAAkB,CAAC;EAChE,CAAC;EACDJ,EAAE,CAACK,SAAS,GAAGC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAG;IACrD,CAACX,IAAI,GAAGtB;EACV,CAAC,GAAG,CAAC,CAAC;EACNyB,EAAE,CAACS,WAAW,GAAG,CAACZ,IAAI,CAAC;EACvB,OAAOG,EAAE;AACX;AACA,eAAeL,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}