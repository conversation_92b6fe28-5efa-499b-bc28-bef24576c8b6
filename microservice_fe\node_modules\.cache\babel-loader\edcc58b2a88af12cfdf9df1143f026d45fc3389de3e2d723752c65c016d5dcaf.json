{"ast": null, "code": "import { buildLocalizeFn } from \"../../_lib/buildLocalizeFn.js\";\nconst eraValues = {\n  narrow: [\"да н.э.\", \"н.э.\"],\n  abbreviated: [\"да н. э.\", \"н. э.\"],\n  wide: [\"да нашай эры\", \"нашай эры\"]\n};\nconst quarterValues = {\n  narrow: [\"1\", \"2\", \"3\", \"4\"],\n  abbreviated: [\"1-ы кв.\", \"2-і кв.\", \"3-і кв.\", \"4-ы кв.\"],\n  wide: [\"1-ы квартал\", \"2-і квартал\", \"3-і квартал\", \"4-ы квартал\"]\n};\nconst monthValues = {\n  narrow: [\"С\", \"Л\", \"С\", \"К\", \"Т\", \"Ч\", \"Л\", \"Ж\", \"В\", \"К\", \"Л\", \"С\"],\n  abbreviated: [\"студз.\", \"лют.\", \"сак.\", \"крас.\", \"трав.\", \"чэрв.\", \"ліп.\", \"жн.\", \"вер.\", \"кастр.\", \"ліст.\", \"сьнеж.\"],\n  wide: [\"студзень\", \"люты\", \"сакавік\", \"красавік\", \"травень\", \"чэрвень\", \"ліпень\", \"жнівень\", \"верасень\", \"кастрычнік\", \"лістапад\", \"сьнежань\"]\n};\nconst formattingMonthValues = {\n  narrow: [\"С\", \"Л\", \"С\", \"К\", \"Т\", \"Ч\", \"Л\", \"Ж\", \"В\", \"К\", \"Л\", \"С\"],\n  abbreviated: [\"студз.\", \"лют.\", \"сак.\", \"крас.\", \"трав.\", \"чэрв.\", \"ліп.\", \"жн.\", \"вер.\", \"кастр.\", \"ліст.\", \"сьнеж.\"],\n  wide: [\"студзеня\", \"лютага\", \"сакавіка\", \"красавіка\", \"траўня\", \"чэрвеня\", \"ліпеня\", \"жніўня\", \"верасня\", \"кастрычніка\", \"лістапада\", \"сьнежня\"]\n};\nconst dayValues = {\n  narrow: [\"Н\", \"П\", \"А\", \"С\", \"Ч\", \"П\", \"С\"],\n  short: [\"нд\", \"пн\", \"аў\", \"ср\", \"чц\", \"пт\", \"сб\"],\n  abbreviated: [\"нядз\", \"пан\", \"аўт\", \"сер\", \"чаць\", \"пят\", \"суб\"],\n  wide: [\"нядзеля\", \"панядзелак\", \"аўторак\", \"серада\", \"чацьвер\", \"пятніца\", \"субота\"]\n};\nconst dayPeriodValues = {\n  narrow: {\n    am: \"ДП\",\n    pm: \"ПП\",\n    midnight: \"поўн.\",\n    noon: \"поўд.\",\n    morning: \"ран.\",\n    afternoon: \"дзень\",\n    evening: \"веч.\",\n    night: \"ноч\"\n  },\n  abbreviated: {\n    am: \"ДП\",\n    pm: \"ПП\",\n    midnight: \"поўн.\",\n    noon: \"поўд.\",\n    morning: \"ран.\",\n    afternoon: \"дзень\",\n    evening: \"веч.\",\n    night: \"ноч\"\n  },\n  wide: {\n    am: \"ДП\",\n    pm: \"ПП\",\n    midnight: \"поўнач\",\n    noon: \"поўдзень\",\n    morning: \"раніца\",\n    afternoon: \"дзень\",\n    evening: \"вечар\",\n    night: \"ноч\"\n  }\n};\nconst formattingDayPeriodValues = {\n  narrow: {\n    am: \"ДП\",\n    pm: \"ПП\",\n    midnight: \"поўн.\",\n    noon: \"поўд.\",\n    morning: \"ран.\",\n    afternoon: \"дня\",\n    evening: \"веч.\",\n    night: \"ночы\"\n  },\n  abbreviated: {\n    am: \"ДП\",\n    pm: \"ПП\",\n    midnight: \"поўн.\",\n    noon: \"поўд.\",\n    morning: \"ран.\",\n    afternoon: \"дня\",\n    evening: \"веч.\",\n    night: \"ночы\"\n  },\n  wide: {\n    am: \"ДП\",\n    pm: \"ПП\",\n    midnight: \"поўнач\",\n    noon: \"поўдзень\",\n    morning: \"раніцы\",\n    afternoon: \"дня\",\n    evening: \"вечара\",\n    night: \"ночы\"\n  }\n};\nconst ordinalNumber = (dirtyNumber, options) => {\n  const unit = String(options === null || options === void 0 ? void 0 : options.unit);\n  const number = Number(dirtyNumber);\n  let suffix;\n\n  /** Though it's an incorrect ordinal form of a date we use it here for consistency with other similar locales (ru, uk)\n   *  For date-month combinations should be used `d` formatter.\n   *  Correct:   `d MMMM` (4 верасня)\n   *  Incorrect: `do MMMM` (4-га верасня)\n   *\n   *  But following the consistency leads to mistakes for literal uses of `do` formatter (ordinal day of month).\n   *  So for phrase \"5th day of month\" (`do дзень месяца`)\n   *  library will produce:            `5-га дзень месяца`\n   *  but correct spelling should be:  `5-ы дзень месяца`\n   *\n   *  So I guess there should be a stand-alone and a formatting version of \"day of month\" formatters\n   */\n  if (unit === \"date\") {\n    suffix = \"-га\";\n  } else if (unit === \"hour\" || unit === \"minute\" || unit === \"second\") {\n    suffix = \"-я\";\n  } else {\n    suffix = (number % 10 === 2 || number % 10 === 3) && number % 100 !== 12 && number % 100 !== 13 ? \"-і\" : \"-ы\";\n  }\n  return number + suffix;\n};\nexport const localize = {\n  ordinalNumber,\n  era: buildLocalizeFn({\n    values: eraValues,\n    defaultWidth: \"wide\"\n  }),\n  quarter: buildLocalizeFn({\n    values: quarterValues,\n    defaultWidth: \"wide\",\n    argumentCallback: quarter => quarter - 1\n  }),\n  month: buildLocalizeFn({\n    values: monthValues,\n    defaultWidth: \"wide\",\n    formattingValues: formattingMonthValues,\n    defaultFormattingWidth: \"wide\"\n  }),\n  day: buildLocalizeFn({\n    values: dayValues,\n    defaultWidth: \"wide\"\n  }),\n  dayPeriod: buildLocalizeFn({\n    values: dayPeriodValues,\n    defaultWidth: \"any\",\n    formattingValues: formattingDayPeriodValues,\n    defaultFormattingWidth: \"wide\"\n  })\n};", "map": {"version": 3, "names": ["buildLocalizeFn", "<PERSON><PERSON><PERSON><PERSON>", "narrow", "abbreviated", "wide", "quarterValues", "month<PERSON><PERSON><PERSON>", "formattingMonthValues", "dayV<PERSON><PERSON>", "short", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "am", "pm", "midnight", "noon", "morning", "afternoon", "evening", "night", "formattingDayPeriodValues", "ordinalNumber", "dirtyNumber", "options", "unit", "String", "number", "Number", "suffix", "localize", "era", "values", "defaultWidth", "quarter", "argument<PERSON>allback", "month", "formattingValues", "defaultFormattingWidth", "day", "<PERSON><PERSON><PERSON><PERSON>"], "sources": ["D:/HeThongCongTyQuanLyNhanCong/Microservice_With_Kubernetes/microservice_fe/node_modules/date-fns/locale/be-tarask/_lib/localize.js"], "sourcesContent": ["import { buildLocalizeFn } from \"../../_lib/buildLocalizeFn.js\";\n\nconst eraValues = {\n  narrow: [\"да н.э.\", \"н.э.\"],\n  abbreviated: [\"да н. э.\", \"н. э.\"],\n  wide: [\"да нашай эры\", \"нашай эры\"],\n};\n\nconst quarterValues = {\n  narrow: [\"1\", \"2\", \"3\", \"4\"],\n  abbreviated: [\"1-ы кв.\", \"2-і кв.\", \"3-і кв.\", \"4-ы кв.\"],\n  wide: [\"1-ы квартал\", \"2-і квартал\", \"3-і квартал\", \"4-ы квартал\"],\n};\n\nconst monthValues = {\n  narrow: [\"С\", \"Л\", \"С\", \"К\", \"Т\", \"Ч\", \"Л\", \"Ж\", \"В\", \"К\", \"Л\", \"С\"],\n  abbreviated: [\n    \"студз.\",\n    \"лют.\",\n    \"сак.\",\n    \"крас.\",\n    \"трав.\",\n    \"чэрв.\",\n    \"ліп.\",\n    \"жн.\",\n    \"вер.\",\n    \"кастр.\",\n    \"ліст.\",\n    \"сьнеж.\",\n  ],\n\n  wide: [\n    \"студзень\",\n    \"люты\",\n    \"сакавік\",\n    \"красавік\",\n    \"травень\",\n    \"чэрвень\",\n    \"ліпень\",\n    \"жнівень\",\n    \"верасень\",\n    \"кастрычнік\",\n    \"лістапад\",\n    \"сьнежань\",\n  ],\n};\nconst formattingMonthValues = {\n  narrow: [\"С\", \"Л\", \"С\", \"К\", \"Т\", \"Ч\", \"Л\", \"Ж\", \"В\", \"К\", \"Л\", \"С\"],\n  abbreviated: [\n    \"студз.\",\n    \"лют.\",\n    \"сак.\",\n    \"крас.\",\n    \"трав.\",\n    \"чэрв.\",\n    \"ліп.\",\n    \"жн.\",\n    \"вер.\",\n    \"кастр.\",\n    \"ліст.\",\n    \"сьнеж.\",\n  ],\n\n  wide: [\n    \"студзеня\",\n    \"лютага\",\n    \"сакавіка\",\n    \"красавіка\",\n    \"траўня\",\n    \"чэрвеня\",\n    \"ліпеня\",\n    \"жніўня\",\n    \"верасня\",\n    \"кастрычніка\",\n    \"лістапада\",\n    \"сьнежня\",\n  ],\n};\n\nconst dayValues = {\n  narrow: [\"Н\", \"П\", \"А\", \"С\", \"Ч\", \"П\", \"С\"],\n  short: [\"нд\", \"пн\", \"аў\", \"ср\", \"чц\", \"пт\", \"сб\"],\n  abbreviated: [\"нядз\", \"пан\", \"аўт\", \"сер\", \"чаць\", \"пят\", \"суб\"],\n  wide: [\n    \"нядзеля\",\n    \"панядзелак\",\n    \"аўторак\",\n    \"серада\",\n    \"чацьвер\",\n    \"пятніца\",\n    \"субота\",\n  ],\n};\n\nconst dayPeriodValues = {\n  narrow: {\n    am: \"ДП\",\n    pm: \"ПП\",\n    midnight: \"поўн.\",\n    noon: \"поўд.\",\n    morning: \"ран.\",\n    afternoon: \"дзень\",\n    evening: \"веч.\",\n    night: \"ноч\",\n  },\n  abbreviated: {\n    am: \"ДП\",\n    pm: \"ПП\",\n    midnight: \"поўн.\",\n    noon: \"поўд.\",\n    morning: \"ран.\",\n    afternoon: \"дзень\",\n    evening: \"веч.\",\n    night: \"ноч\",\n  },\n  wide: {\n    am: \"ДП\",\n    pm: \"ПП\",\n    midnight: \"поўнач\",\n    noon: \"поўдзень\",\n    morning: \"раніца\",\n    afternoon: \"дзень\",\n    evening: \"вечар\",\n    night: \"ноч\",\n  },\n};\nconst formattingDayPeriodValues = {\n  narrow: {\n    am: \"ДП\",\n    pm: \"ПП\",\n    midnight: \"поўн.\",\n    noon: \"поўд.\",\n    morning: \"ран.\",\n    afternoon: \"дня\",\n    evening: \"веч.\",\n    night: \"ночы\",\n  },\n  abbreviated: {\n    am: \"ДП\",\n    pm: \"ПП\",\n    midnight: \"поўн.\",\n    noon: \"поўд.\",\n    morning: \"ран.\",\n    afternoon: \"дня\",\n    evening: \"веч.\",\n    night: \"ночы\",\n  },\n  wide: {\n    am: \"ДП\",\n    pm: \"ПП\",\n    midnight: \"поўнач\",\n    noon: \"поўдзень\",\n    morning: \"раніцы\",\n    afternoon: \"дня\",\n    evening: \"вечара\",\n    night: \"ночы\",\n  },\n};\n\nconst ordinalNumber = (dirtyNumber, options) => {\n  const unit = String(options?.unit);\n  const number = Number(dirtyNumber);\n  let suffix;\n\n  /** Though it's an incorrect ordinal form of a date we use it here for consistency with other similar locales (ru, uk)\n   *  For date-month combinations should be used `d` formatter.\n   *  Correct:   `d MMMM` (4 верасня)\n   *  Incorrect: `do MMMM` (4-га верасня)\n   *\n   *  But following the consistency leads to mistakes for literal uses of `do` formatter (ordinal day of month).\n   *  So for phrase \"5th day of month\" (`do дзень месяца`)\n   *  library will produce:            `5-га дзень месяца`\n   *  but correct spelling should be:  `5-ы дзень месяца`\n   *\n   *  So I guess there should be a stand-alone and a formatting version of \"day of month\" formatters\n   */\n  if (unit === \"date\") {\n    suffix = \"-га\";\n  } else if (unit === \"hour\" || unit === \"minute\" || unit === \"second\") {\n    suffix = \"-я\";\n  } else {\n    suffix =\n      (number % 10 === 2 || number % 10 === 3) &&\n      number % 100 !== 12 &&\n      number % 100 !== 13\n        ? \"-і\"\n        : \"-ы\";\n  }\n\n  return number + suffix;\n};\n\nexport const localize = {\n  ordinalNumber,\n\n  era: buildLocalizeFn({\n    values: eraValues,\n    defaultWidth: \"wide\",\n  }),\n\n  quarter: buildLocalizeFn({\n    values: quarterValues,\n    defaultWidth: \"wide\",\n    argumentCallback: (quarter) => quarter - 1,\n  }),\n\n  month: buildLocalizeFn({\n    values: monthValues,\n    defaultWidth: \"wide\",\n    formattingValues: formattingMonthValues,\n    defaultFormattingWidth: \"wide\",\n  }),\n\n  day: buildLocalizeFn({\n    values: dayValues,\n    defaultWidth: \"wide\",\n  }),\n\n  dayPeriod: buildLocalizeFn({\n    values: dayPeriodValues,\n    defaultWidth: \"any\",\n    formattingValues: formattingDayPeriodValues,\n    defaultFormattingWidth: \"wide\",\n  }),\n};\n"], "mappings": "AAAA,SAASA,eAAe,QAAQ,+BAA+B;AAE/D,MAAMC,SAAS,GAAG;EAChBC,MAAM,EAAE,CAAC,SAAS,EAAE,MAAM,CAAC;EAC3BC,WAAW,EAAE,CAAC,UAAU,EAAE,OAAO,CAAC;EAClCC,IAAI,EAAE,CAAC,cAAc,EAAE,WAAW;AACpC,CAAC;AAED,MAAMC,aAAa,GAAG;EACpBH,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EAC5BC,WAAW,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC;EACzDC,IAAI,EAAE,CAAC,aAAa,EAAE,aAAa,EAAE,aAAa,EAAE,aAAa;AACnE,CAAC;AAED,MAAME,WAAW,GAAG;EAClBJ,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EACpEC,WAAW,EAAE,CACX,QAAQ,EACR,MAAM,EACN,MAAM,EACN,OAAO,EACP,OAAO,EACP,OAAO,EACP,MAAM,EACN,KAAK,EACL,MAAM,EACN,QAAQ,EACR,OAAO,EACP,QAAQ,CACT;EAEDC,IAAI,EAAE,CACJ,UAAU,EACV,MAAM,EACN,SAAS,EACT,UAAU,EACV,SAAS,EACT,SAAS,EACT,QAAQ,EACR,SAAS,EACT,UAAU,EACV,YAAY,EACZ,UAAU,EACV,UAAU;AAEd,CAAC;AACD,MAAMG,qBAAqB,GAAG;EAC5BL,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EACpEC,WAAW,EAAE,CACX,QAAQ,EACR,MAAM,EACN,MAAM,EACN,OAAO,EACP,OAAO,EACP,OAAO,EACP,MAAM,EACN,KAAK,EACL,MAAM,EACN,QAAQ,EACR,OAAO,EACP,QAAQ,CACT;EAEDC,IAAI,EAAE,CACJ,UAAU,EACV,QAAQ,EACR,UAAU,EACV,WAAW,EACX,QAAQ,EACR,SAAS,EACT,QAAQ,EACR,QAAQ,EACR,SAAS,EACT,aAAa,EACb,WAAW,EACX,SAAS;AAEb,CAAC;AAED,MAAMI,SAAS,GAAG;EAChBN,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EAC3CO,KAAK,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EACjDN,WAAW,EAAE,CAAC,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,CAAC;EAChEC,IAAI,EAAE,CACJ,SAAS,EACT,YAAY,EACZ,SAAS,EACT,QAAQ,EACR,SAAS,EACT,SAAS,EACT,QAAQ;AAEZ,CAAC;AAED,MAAMM,eAAe,GAAG;EACtBR,MAAM,EAAE;IACNS,EAAE,EAAE,IAAI;IACRC,EAAE,EAAE,IAAI;IACRC,QAAQ,EAAE,OAAO;IACjBC,IAAI,EAAE,OAAO;IACbC,OAAO,EAAE,MAAM;IACfC,SAAS,EAAE,OAAO;IAClBC,OAAO,EAAE,MAAM;IACfC,KAAK,EAAE;EACT,CAAC;EACDf,WAAW,EAAE;IACXQ,EAAE,EAAE,IAAI;IACRC,EAAE,EAAE,IAAI;IACRC,QAAQ,EAAE,OAAO;IACjBC,IAAI,EAAE,OAAO;IACbC,OAAO,EAAE,MAAM;IACfC,SAAS,EAAE,OAAO;IAClBC,OAAO,EAAE,MAAM;IACfC,KAAK,EAAE;EACT,CAAC;EACDd,IAAI,EAAE;IACJO,EAAE,EAAE,IAAI;IACRC,EAAE,EAAE,IAAI;IACRC,QAAQ,EAAE,QAAQ;IAClBC,IAAI,EAAE,UAAU;IAChBC,OAAO,EAAE,QAAQ;IACjBC,SAAS,EAAE,OAAO;IAClBC,OAAO,EAAE,OAAO;IAChBC,KAAK,EAAE;EACT;AACF,CAAC;AACD,MAAMC,yBAAyB,GAAG;EAChCjB,MAAM,EAAE;IACNS,EAAE,EAAE,IAAI;IACRC,EAAE,EAAE,IAAI;IACRC,QAAQ,EAAE,OAAO;IACjBC,IAAI,EAAE,OAAO;IACbC,OAAO,EAAE,MAAM;IACfC,SAAS,EAAE,KAAK;IAChBC,OAAO,EAAE,MAAM;IACfC,KAAK,EAAE;EACT,CAAC;EACDf,WAAW,EAAE;IACXQ,EAAE,EAAE,IAAI;IACRC,EAAE,EAAE,IAAI;IACRC,QAAQ,EAAE,OAAO;IACjBC,IAAI,EAAE,OAAO;IACbC,OAAO,EAAE,MAAM;IACfC,SAAS,EAAE,KAAK;IAChBC,OAAO,EAAE,MAAM;IACfC,KAAK,EAAE;EACT,CAAC;EACDd,IAAI,EAAE;IACJO,EAAE,EAAE,IAAI;IACRC,EAAE,EAAE,IAAI;IACRC,QAAQ,EAAE,QAAQ;IAClBC,IAAI,EAAE,UAAU;IAChBC,OAAO,EAAE,QAAQ;IACjBC,SAAS,EAAE,KAAK;IAChBC,OAAO,EAAE,QAAQ;IACjBC,KAAK,EAAE;EACT;AACF,CAAC;AAED,MAAME,aAAa,GAAGA,CAACC,WAAW,EAAEC,OAAO,KAAK;EAC9C,MAAMC,IAAI,GAAGC,MAAM,CAACF,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEC,IAAI,CAAC;EAClC,MAAME,MAAM,GAAGC,MAAM,CAACL,WAAW,CAAC;EAClC,IAAIM,MAAM;;EAEV;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,IAAIJ,IAAI,KAAK,MAAM,EAAE;IACnBI,MAAM,GAAG,KAAK;EAChB,CAAC,MAAM,IAAIJ,IAAI,KAAK,MAAM,IAAIA,IAAI,KAAK,QAAQ,IAAIA,IAAI,KAAK,QAAQ,EAAE;IACpEI,MAAM,GAAG,IAAI;EACf,CAAC,MAAM;IACLA,MAAM,GACJ,CAACF,MAAM,GAAG,EAAE,KAAK,CAAC,IAAIA,MAAM,GAAG,EAAE,KAAK,CAAC,KACvCA,MAAM,GAAG,GAAG,KAAK,EAAE,IACnBA,MAAM,GAAG,GAAG,KAAK,EAAE,GACf,IAAI,GACJ,IAAI;EACZ;EAEA,OAAOA,MAAM,GAAGE,MAAM;AACxB,CAAC;AAED,OAAO,MAAMC,QAAQ,GAAG;EACtBR,aAAa;EAEbS,GAAG,EAAE7B,eAAe,CAAC;IACnB8B,MAAM,EAAE7B,SAAS;IACjB8B,YAAY,EAAE;EAChB,CAAC,CAAC;EAEFC,OAAO,EAAEhC,eAAe,CAAC;IACvB8B,MAAM,EAAEzB,aAAa;IACrB0B,YAAY,EAAE,MAAM;IACpBE,gBAAgB,EAAGD,OAAO,IAAKA,OAAO,GAAG;EAC3C,CAAC,CAAC;EAEFE,KAAK,EAAElC,eAAe,CAAC;IACrB8B,MAAM,EAAExB,WAAW;IACnByB,YAAY,EAAE,MAAM;IACpBI,gBAAgB,EAAE5B,qBAAqB;IACvC6B,sBAAsB,EAAE;EAC1B,CAAC,CAAC;EAEFC,GAAG,EAAErC,eAAe,CAAC;IACnB8B,MAAM,EAAEtB,SAAS;IACjBuB,YAAY,EAAE;EAChB,CAAC,CAAC;EAEFO,SAAS,EAAEtC,eAAe,CAAC;IACzB8B,MAAM,EAAEpB,eAAe;IACvBqB,YAAY,EAAE,KAAK;IACnBI,gBAAgB,EAAEhB,yBAAyB;IAC3CiB,sBAAsB,EAAE;EAC1B,CAAC;AACH,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}