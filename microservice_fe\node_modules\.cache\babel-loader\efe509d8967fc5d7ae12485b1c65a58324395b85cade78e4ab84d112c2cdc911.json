{"ast": null, "code": "import getWindow from \"./getWindow.js\";\nimport getDocumentElement from \"./getDocumentElement.js\";\nimport getWindowScrollBarX from \"./getWindowScrollBarX.js\";\nimport isLayoutViewport from \"./isLayoutViewport.js\";\nexport default function getViewportRect(element, strategy) {\n  var win = getWindow(element);\n  var html = getDocumentElement(element);\n  var visualViewport = win.visualViewport;\n  var width = html.clientWidth;\n  var height = html.clientHeight;\n  var x = 0;\n  var y = 0;\n  if (visualViewport) {\n    width = visualViewport.width;\n    height = visualViewport.height;\n    var layoutViewport = isLayoutViewport();\n    if (layoutViewport || !layoutViewport && strategy === 'fixed') {\n      x = visualViewport.offsetLeft;\n      y = visualViewport.offsetTop;\n    }\n  }\n  return {\n    width: width,\n    height: height,\n    x: x + getWindowScrollBarX(element),\n    y: y\n  };\n}", "map": {"version": 3, "names": ["getWindow", "getDocumentElement", "getWindowScrollBarX", "isLayoutViewport", "getViewportRect", "element", "strategy", "win", "html", "visualViewport", "width", "clientWidth", "height", "clientHeight", "x", "y", "layoutViewport", "offsetLeft", "offsetTop"], "sources": ["D:/HeThongCongTyQuanLyNhanCong/Microservice_With_Kubernetes/microservice_fe/node_modules/@popperjs/core/lib/dom-utils/getViewportRect.js"], "sourcesContent": ["import getWindow from \"./getWindow.js\";\nimport getDocumentElement from \"./getDocumentElement.js\";\nimport getWindowScrollBarX from \"./getWindowScrollBarX.js\";\nimport isLayoutViewport from \"./isLayoutViewport.js\";\nexport default function getViewportRect(element, strategy) {\n  var win = getWindow(element);\n  var html = getDocumentElement(element);\n  var visualViewport = win.visualViewport;\n  var width = html.clientWidth;\n  var height = html.clientHeight;\n  var x = 0;\n  var y = 0;\n\n  if (visualViewport) {\n    width = visualViewport.width;\n    height = visualViewport.height;\n    var layoutViewport = isLayoutViewport();\n\n    if (layoutViewport || !layoutViewport && strategy === 'fixed') {\n      x = visualViewport.offsetLeft;\n      y = visualViewport.offsetTop;\n    }\n  }\n\n  return {\n    width: width,\n    height: height,\n    x: x + getWindowScrollBarX(element),\n    y: y\n  };\n}"], "mappings": "AAAA,OAAOA,SAAS,MAAM,gBAAgB;AACtC,OAAOC,kBAAkB,MAAM,yBAAyB;AACxD,OAAOC,mBAAmB,MAAM,0BAA0B;AAC1D,OAAOC,gBAAgB,MAAM,uBAAuB;AACpD,eAAe,SAASC,eAAeA,CAACC,OAAO,EAAEC,QAAQ,EAAE;EACzD,IAAIC,GAAG,GAAGP,SAAS,CAACK,OAAO,CAAC;EAC5B,IAAIG,IAAI,GAAGP,kBAAkB,CAACI,OAAO,CAAC;EACtC,IAAII,cAAc,GAAGF,GAAG,CAACE,cAAc;EACvC,IAAIC,KAAK,GAAGF,IAAI,CAACG,WAAW;EAC5B,IAAIC,MAAM,GAAGJ,IAAI,CAACK,YAAY;EAC9B,IAAIC,CAAC,GAAG,CAAC;EACT,IAAIC,CAAC,GAAG,CAAC;EAET,IAAIN,cAAc,EAAE;IAClBC,KAAK,GAAGD,cAAc,CAACC,KAAK;IAC5BE,MAAM,GAAGH,cAAc,CAACG,MAAM;IAC9B,IAAII,cAAc,GAAGb,gBAAgB,CAAC,CAAC;IAEvC,IAAIa,cAAc,IAAI,CAACA,cAAc,IAAIV,QAAQ,KAAK,OAAO,EAAE;MAC7DQ,CAAC,GAAGL,cAAc,CAACQ,UAAU;MAC7BF,CAAC,GAAGN,cAAc,CAACS,SAAS;IAC9B;EACF;EAEA,OAAO;IACLR,KAAK,EAAEA,KAAK;IACZE,MAAM,EAAEA,MAAM;IACdE,CAAC,EAAEA,CAAC,GAAGZ,mBAAmB,CAACG,OAAO,CAAC;IACnCU,CAAC,EAAEA;EACL,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}