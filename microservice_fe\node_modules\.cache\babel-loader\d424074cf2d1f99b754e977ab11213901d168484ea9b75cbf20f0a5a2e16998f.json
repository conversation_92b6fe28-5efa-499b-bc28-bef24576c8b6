{"ast": null, "code": "import { buildMatchFn } from \"../../_lib/buildMatchFn.js\";\nimport { buildMatchPatternFn } from \"../../_lib/buildMatchPatternFn.js\";\nconst matchOrdinalNumberPattern = /^(\\d+)(º)?/i;\nconst parseOrdinalNumberPattern = /\\d+/i;\nconst matchEraPatterns = {\n  narrow: /^(aC|dC)/i,\n  abbreviated: /^(a\\.?\\s?C\\.?|a\\.?\\s?e\\.?\\s?v\\.?|d\\.?\\s?C\\.?|e\\.?\\s?v\\.?)/i,\n  wide: /^(avanti Cristo|avanti Era Volgare|dopo Cristo|Era Volgare)/i\n};\nconst parseEraPatterns = {\n  any: [/^a/i, /^(d|e)/i]\n};\nconst matchQuarterPatterns = {\n  narrow: /^[1234]/i,\n  abbreviated: /^t[1234]/i,\n  wide: /^[1234](º)? trimestre/i\n};\nconst parseQuarterPatterns = {\n  any: [/1/i, /2/i, /3/i, /4/i]\n};\nconst matchMonthPatterns = {\n  narrow: /^[gfmalsond]/i,\n  abbreviated: /^(gen|feb|mar|apr|mag|giu|lug|ago|set|ott|nov|dic)/i,\n  wide: /^(gennaio|febbraio|marzo|aprile|maggio|giugno|luglio|agosto|settembre|ottobre|novembre|dicembre)/i\n};\nconst parseMonthPatterns = {\n  narrow: [/^g/i, /^f/i, /^m/i, /^a/i, /^m/i, /^g/i, /^l/i, /^a/i, /^s/i, /^o/i, /^n/i, /^d/i],\n  any: [/^ge/i, /^f/i, /^mar/i, /^ap/i, /^mag/i, /^gi/i, /^l/i, /^ag/i, /^s/i, /^o/i, /^n/i, /^d/i]\n};\nconst matchDayPatterns = {\n  narrow: /^[dlmgvs]/i,\n  short: /^(do|lu|ma|me|gi|ve|sa)/i,\n  abbreviated: /^(dom|lun|mar|mer|gio|ven|sab)/i,\n  wide: /^(domenica|luned[i|ì]|marted[i|ì]|mercoled[i|ì]|gioved[i|ì]|venerd[i|ì]|sabato)/i\n};\nconst parseDayPatterns = {\n  narrow: [/^d/i, /^l/i, /^m/i, /^m/i, /^g/i, /^v/i, /^s/i],\n  any: [/^d/i, /^l/i, /^ma/i, /^me/i, /^g/i, /^v/i, /^s/i]\n};\nconst matchDayPeriodPatterns = {\n  narrow: /^(a|m\\.|p|mezzanotte|mezzogiorno|(di|del) (mattina|pomeriggio|sera|notte))/i,\n  any: /^([ap]\\.?\\s?m\\.?|mezzanotte|mezzogiorno|(di|del) (mattina|pomeriggio|sera|notte))/i\n};\nconst parseDayPeriodPatterns = {\n  any: {\n    am: /^a/i,\n    pm: /^p/i,\n    midnight: /^mezza/i,\n    noon: /^mezzo/i,\n    morning: /mattina/i,\n    afternoon: /pomeriggio/i,\n    evening: /sera/i,\n    night: /notte/i\n  }\n};\nexport const match = {\n  ordinalNumber: buildMatchPatternFn({\n    matchPattern: matchOrdinalNumberPattern,\n    parsePattern: parseOrdinalNumberPattern,\n    valueCallback: value => parseInt(value, 10)\n  }),\n  era: buildMatchFn({\n    matchPatterns: matchEraPatterns,\n    defaultMatchWidth: \"wide\",\n    parsePatterns: parseEraPatterns,\n    defaultParseWidth: \"any\"\n  }),\n  quarter: buildMatchFn({\n    matchPatterns: matchQuarterPatterns,\n    defaultMatchWidth: \"wide\",\n    parsePatterns: parseQuarterPatterns,\n    defaultParseWidth: \"any\",\n    valueCallback: index => index + 1\n  }),\n  month: buildMatchFn({\n    matchPatterns: matchMonthPatterns,\n    defaultMatchWidth: \"wide\",\n    parsePatterns: parseMonthPatterns,\n    defaultParseWidth: \"any\"\n  }),\n  day: buildMatchFn({\n    matchPatterns: matchDayPatterns,\n    defaultMatchWidth: \"wide\",\n    parsePatterns: parseDayPatterns,\n    defaultParseWidth: \"any\"\n  }),\n  dayPeriod: buildMatchFn({\n    matchPatterns: matchDayPeriodPatterns,\n    defaultMatchWidth: \"any\",\n    parsePatterns: parseDayPeriodPatterns,\n    defaultParseWidth: \"any\"\n  })\n};", "map": {"version": 3, "names": ["buildMatchFn", "buildMatchPatternFn", "matchOrdinalNumberPattern", "parseOrdinalNumberPattern", "matchEraPatterns", "narrow", "abbreviated", "wide", "parseEraPatterns", "any", "matchQuarterPatterns", "parseQuarterPatterns", "matchMonthPatterns", "parseMonthPatterns", "matchDayPatterns", "short", "parseDayPatterns", "matchDayPeriodPatterns", "parseDayPeriodPatterns", "am", "pm", "midnight", "noon", "morning", "afternoon", "evening", "night", "match", "ordinalNumber", "matchPattern", "parsePattern", "valueCallback", "value", "parseInt", "era", "matchPatterns", "defaultMatchWidth", "parsePatterns", "defaultParseWidth", "quarter", "index", "month", "day", "<PERSON><PERSON><PERSON><PERSON>"], "sources": ["D:/HeThongCongTyQuanLyNhanCong/Microservice_With_Kubernetes/microservice_fe/node_modules/date-fns/locale/it/_lib/match.js"], "sourcesContent": ["import { buildMatchFn } from \"../../_lib/buildMatchFn.js\";\nimport { buildMatchPatternFn } from \"../../_lib/buildMatchPatternFn.js\";\n\nconst matchOrdinalNumberPattern = /^(\\d+)(º)?/i;\nconst parseOrdinalNumberPattern = /\\d+/i;\n\nconst matchEraPatterns = {\n  narrow: /^(aC|dC)/i,\n  abbreviated: /^(a\\.?\\s?C\\.?|a\\.?\\s?e\\.?\\s?v\\.?|d\\.?\\s?C\\.?|e\\.?\\s?v\\.?)/i,\n  wide: /^(avanti Cristo|avanti Era Volgare|dopo Cristo|Era Volgare)/i,\n};\nconst parseEraPatterns = {\n  any: [/^a/i, /^(d|e)/i],\n};\n\nconst matchQuarterPatterns = {\n  narrow: /^[1234]/i,\n  abbreviated: /^t[1234]/i,\n  wide: /^[1234](º)? trimestre/i,\n};\nconst parseQuarterPatterns = {\n  any: [/1/i, /2/i, /3/i, /4/i],\n};\n\nconst matchMonthPatterns = {\n  narrow: /^[gfmalsond]/i,\n  abbreviated: /^(gen|feb|mar|apr|mag|giu|lug|ago|set|ott|nov|dic)/i,\n  wide: /^(gennaio|febbraio|marzo|aprile|maggio|giugno|luglio|agosto|settembre|ottobre|novembre|dicembre)/i,\n};\nconst parseMonthPatterns = {\n  narrow: [\n    /^g/i,\n    /^f/i,\n    /^m/i,\n    /^a/i,\n    /^m/i,\n    /^g/i,\n    /^l/i,\n    /^a/i,\n    /^s/i,\n    /^o/i,\n    /^n/i,\n    /^d/i,\n  ],\n\n  any: [\n    /^ge/i,\n    /^f/i,\n    /^mar/i,\n    /^ap/i,\n    /^mag/i,\n    /^gi/i,\n    /^l/i,\n    /^ag/i,\n    /^s/i,\n    /^o/i,\n    /^n/i,\n    /^d/i,\n  ],\n};\n\nconst matchDayPatterns = {\n  narrow: /^[dlmgvs]/i,\n  short: /^(do|lu|ma|me|gi|ve|sa)/i,\n  abbreviated: /^(dom|lun|mar|mer|gio|ven|sab)/i,\n  wide: /^(domenica|luned[i|ì]|marted[i|ì]|mercoled[i|ì]|gioved[i|ì]|venerd[i|ì]|sabato)/i,\n};\nconst parseDayPatterns = {\n  narrow: [/^d/i, /^l/i, /^m/i, /^m/i, /^g/i, /^v/i, /^s/i],\n  any: [/^d/i, /^l/i, /^ma/i, /^me/i, /^g/i, /^v/i, /^s/i],\n};\n\nconst matchDayPeriodPatterns = {\n  narrow:\n    /^(a|m\\.|p|mezzanotte|mezzogiorno|(di|del) (mattina|pomeriggio|sera|notte))/i,\n  any: /^([ap]\\.?\\s?m\\.?|mezzanotte|mezzogiorno|(di|del) (mattina|pomeriggio|sera|notte))/i,\n};\nconst parseDayPeriodPatterns = {\n  any: {\n    am: /^a/i,\n    pm: /^p/i,\n    midnight: /^mezza/i,\n    noon: /^mezzo/i,\n    morning: /mattina/i,\n    afternoon: /pomeriggio/i,\n    evening: /sera/i,\n    night: /notte/i,\n  },\n};\n\nexport const match = {\n  ordinalNumber: buildMatchPatternFn({\n    matchPattern: matchOrdinalNumberPattern,\n    parsePattern: parseOrdinalNumberPattern,\n    valueCallback: (value) => parseInt(value, 10),\n  }),\n\n  era: buildMatchFn({\n    matchPatterns: matchEraPatterns,\n    defaultMatchWidth: \"wide\",\n    parsePatterns: parseEraPatterns,\n    defaultParseWidth: \"any\",\n  }),\n\n  quarter: buildMatchFn({\n    matchPatterns: matchQuarterPatterns,\n    defaultMatchWidth: \"wide\",\n    parsePatterns: parseQuarterPatterns,\n    defaultParseWidth: \"any\",\n    valueCallback: (index) => index + 1,\n  }),\n\n  month: buildMatchFn({\n    matchPatterns: matchMonthPatterns,\n    defaultMatchWidth: \"wide\",\n    parsePatterns: parseMonthPatterns,\n    defaultParseWidth: \"any\",\n  }),\n\n  day: buildMatchFn({\n    matchPatterns: matchDayPatterns,\n    defaultMatchWidth: \"wide\",\n    parsePatterns: parseDayPatterns,\n    defaultParseWidth: \"any\",\n  }),\n\n  dayPeriod: buildMatchFn({\n    matchPatterns: matchDayPeriodPatterns,\n    defaultMatchWidth: \"any\",\n    parsePatterns: parseDayPeriodPatterns,\n    defaultParseWidth: \"any\",\n  }),\n};\n"], "mappings": "AAAA,SAASA,YAAY,QAAQ,4BAA4B;AACzD,SAASC,mBAAmB,QAAQ,mCAAmC;AAEvE,MAAMC,yBAAyB,GAAG,aAAa;AAC/C,MAAMC,yBAAyB,GAAG,MAAM;AAExC,MAAMC,gBAAgB,GAAG;EACvBC,MAAM,EAAE,WAAW;EACnBC,WAAW,EAAE,4DAA4D;EACzEC,IAAI,EAAE;AACR,CAAC;AACD,MAAMC,gBAAgB,GAAG;EACvBC,GAAG,EAAE,CAAC,KAAK,EAAE,SAAS;AACxB,CAAC;AAED,MAAMC,oBAAoB,GAAG;EAC3BL,MAAM,EAAE,UAAU;EAClBC,WAAW,EAAE,WAAW;EACxBC,IAAI,EAAE;AACR,CAAC;AACD,MAAMI,oBAAoB,GAAG;EAC3BF,GAAG,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI;AAC9B,CAAC;AAED,MAAMG,kBAAkB,GAAG;EACzBP,MAAM,EAAE,eAAe;EACvBC,WAAW,EAAE,qDAAqD;EAClEC,IAAI,EAAE;AACR,CAAC;AACD,MAAMM,kBAAkB,GAAG;EACzBR,MAAM,EAAE,CACN,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,CACN;EAEDI,GAAG,EAAE,CACH,MAAM,EACN,KAAK,EACL,OAAO,EACP,MAAM,EACN,OAAO,EACP,MAAM,EACN,KAAK,EACL,MAAM,EACN,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK;AAET,CAAC;AAED,MAAMK,gBAAgB,GAAG;EACvBT,MAAM,EAAE,YAAY;EACpBU,KAAK,EAAE,0BAA0B;EACjCT,WAAW,EAAE,iCAAiC;EAC9CC,IAAI,EAAE;AACR,CAAC;AACD,MAAMS,gBAAgB,GAAG;EACvBX,MAAM,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;EACzDI,GAAG,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK;AACzD,CAAC;AAED,MAAMQ,sBAAsB,GAAG;EAC7BZ,MAAM,EACJ,6EAA6E;EAC/EI,GAAG,EAAE;AACP,CAAC;AACD,MAAMS,sBAAsB,GAAG;EAC7BT,GAAG,EAAE;IACHU,EAAE,EAAE,KAAK;IACTC,EAAE,EAAE,KAAK;IACTC,QAAQ,EAAE,SAAS;IACnBC,IAAI,EAAE,SAAS;IACfC,OAAO,EAAE,UAAU;IACnBC,SAAS,EAAE,aAAa;IACxBC,OAAO,EAAE,OAAO;IAChBC,KAAK,EAAE;EACT;AACF,CAAC;AAED,OAAO,MAAMC,KAAK,GAAG;EACnBC,aAAa,EAAE3B,mBAAmB,CAAC;IACjC4B,YAAY,EAAE3B,yBAAyB;IACvC4B,YAAY,EAAE3B,yBAAyB;IACvC4B,aAAa,EAAGC,KAAK,IAAKC,QAAQ,CAACD,KAAK,EAAE,EAAE;EAC9C,CAAC,CAAC;EAEFE,GAAG,EAAElC,YAAY,CAAC;IAChBmC,aAAa,EAAE/B,gBAAgB;IAC/BgC,iBAAiB,EAAE,MAAM;IACzBC,aAAa,EAAE7B,gBAAgB;IAC/B8B,iBAAiB,EAAE;EACrB,CAAC,CAAC;EAEFC,OAAO,EAAEvC,YAAY,CAAC;IACpBmC,aAAa,EAAEzB,oBAAoB;IACnC0B,iBAAiB,EAAE,MAAM;IACzBC,aAAa,EAAE1B,oBAAoB;IACnC2B,iBAAiB,EAAE,KAAK;IACxBP,aAAa,EAAGS,KAAK,IAAKA,KAAK,GAAG;EACpC,CAAC,CAAC;EAEFC,KAAK,EAAEzC,YAAY,CAAC;IAClBmC,aAAa,EAAEvB,kBAAkB;IACjCwB,iBAAiB,EAAE,MAAM;IACzBC,aAAa,EAAExB,kBAAkB;IACjCyB,iBAAiB,EAAE;EACrB,CAAC,CAAC;EAEFI,GAAG,EAAE1C,YAAY,CAAC;IAChBmC,aAAa,EAAErB,gBAAgB;IAC/BsB,iBAAiB,EAAE,MAAM;IACzBC,aAAa,EAAErB,gBAAgB;IAC/BsB,iBAAiB,EAAE;EACrB,CAAC,CAAC;EAEFK,SAAS,EAAE3C,YAAY,CAAC;IACtBmC,aAAa,EAAElB,sBAAsB;IACrCmB,iBAAiB,EAAE,KAAK;IACxBC,aAAa,EAAEnB,sBAAsB;IACrCoB,iBAAiB,EAAE;EACrB,CAAC;AACH,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}