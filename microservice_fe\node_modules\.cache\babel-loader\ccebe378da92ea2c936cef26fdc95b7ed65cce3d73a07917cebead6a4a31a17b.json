{"ast": null, "code": "export default function HTMLElementType(props, propName, componentName, location, propFullName) {\n  if (process.env.NODE_ENV === 'production') {\n    return null;\n  }\n  const propValue = props[propName];\n  const safePropName = propFullName || propName;\n  if (propValue == null) {\n    return null;\n  }\n  if (propValue && propValue.nodeType !== 1) {\n    return new Error(`Invalid ${location} \\`${safePropName}\\` supplied to \\`${componentName}\\`. ` + `Expected an HTMLElement.`);\n  }\n  return null;\n}", "map": {"version": 3, "names": ["HTMLElementType", "props", "propName", "componentName", "location", "prop<PERSON><PERSON><PERSON><PERSON>", "process", "env", "NODE_ENV", "propValue", "safePropName", "nodeType", "Error"], "sources": ["D:/HeThongCongTyQuanLyNhanCong/Microservice_With_Kubernetes/microservice_fe/node_modules/@mui/utils/esm/HTMLElementType/HTMLElementType.js"], "sourcesContent": ["export default function HTMLElementType(props, propName, componentName, location, propFullName) {\n  if (process.env.NODE_ENV === 'production') {\n    return null;\n  }\n  const propValue = props[propName];\n  const safePropName = propFullName || propName;\n  if (propValue == null) {\n    return null;\n  }\n  if (propValue && propValue.nodeType !== 1) {\n    return new Error(`Invalid ${location} \\`${safePropName}\\` supplied to \\`${componentName}\\`. ` + `Expected an HTMLElement.`);\n  }\n  return null;\n}"], "mappings": "AAAA,eAAe,SAASA,eAAeA,CAACC,KAAK,EAAEC,QAAQ,EAAEC,aAAa,EAAEC,QAAQ,EAAEC,YAAY,EAAE;EAC9F,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzC,OAAO,IAAI;EACb;EACA,MAAMC,SAAS,GAAGR,KAAK,CAACC,QAAQ,CAAC;EACjC,MAAMQ,YAAY,GAAGL,YAAY,IAAIH,QAAQ;EAC7C,IAAIO,SAAS,IAAI,IAAI,EAAE;IACrB,OAAO,IAAI;EACb;EACA,IAAIA,SAAS,IAAIA,SAAS,CAACE,QAAQ,KAAK,CAAC,EAAE;IACzC,OAAO,IAAIC,KAAK,CAAC,WAAWR,QAAQ,MAAMM,YAAY,oBAAoBP,aAAa,MAAM,GAAG,0BAA0B,CAAC;EAC7H;EACA,OAAO,IAAI;AACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}