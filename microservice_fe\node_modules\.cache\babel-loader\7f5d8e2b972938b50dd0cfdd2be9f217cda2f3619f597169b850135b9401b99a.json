{"ast": null, "code": "'use client';\n\nimport * as React from 'react';\nimport { ThemeContext } from '@mui/styled-engine';\nfunction isObjectEmpty(obj) {\n  return Object.keys(obj).length === 0;\n}\nfunction useTheme(defaultTheme = null) {\n  const contextTheme = React.useContext(ThemeContext);\n  return !contextTheme || isObjectEmpty(contextTheme) ? defaultTheme : contextTheme;\n}\nexport default useTheme;", "map": {"version": 3, "names": ["React", "ThemeContext", "isObjectEmpty", "obj", "Object", "keys", "length", "useTheme", "defaultTheme", "contextTheme", "useContext"], "sources": ["D:/HeThongCongTyQuanLyNhanCong/Microservice_With_Kubernetes/microservice_fe/node_modules/@mui/system/esm/useThemeWithoutDefault/useThemeWithoutDefault.js"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\nimport { ThemeContext } from '@mui/styled-engine';\nfunction isObjectEmpty(obj) {\n  return Object.keys(obj).length === 0;\n}\nfunction useTheme(defaultTheme = null) {\n  const contextTheme = React.useContext(ThemeContext);\n  return !contextTheme || isObjectEmpty(contextTheme) ? defaultTheme : contextTheme;\n}\nexport default useTheme;"], "mappings": "AAAA,YAAY;;AAEZ,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,YAAY,QAAQ,oBAAoB;AACjD,SAASC,aAAaA,CAACC,GAAG,EAAE;EAC1B,OAAOC,MAAM,CAACC,IAAI,CAACF,GAAG,CAAC,CAACG,MAAM,KAAK,CAAC;AACtC;AACA,SAASC,QAAQA,CAACC,YAAY,GAAG,IAAI,EAAE;EACrC,MAAMC,YAAY,GAAGT,KAAK,CAACU,UAAU,CAACT,YAAY,CAAC;EACnD,OAAO,CAACQ,YAAY,IAAIP,aAAa,CAACO,YAAY,CAAC,GAAGD,YAAY,GAAGC,YAAY;AACnF;AACA,eAAeF,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}