{"ast": null, "code": "const warnedOnceCache = new Set();\n\n/**\n * Logs a message to the console on development mode. The warning will only be logged once.\n *\n * The message is the log's cache key. Two identical messages will only be logged once.\n *\n * This function is a no-op in production.\n *\n * @param message the message to log\n * @param gravity the gravity of the warning. Defaults to `'warning'`.\n * @returns\n */\nexport function warnOnce(message, gravity = 'warning') {\n  if (process.env.NODE_ENV === 'production') {\n    return;\n  }\n  const cleanMessage = Array.isArray(message) ? message.join('\\n') : message;\n  if (!warnedOnceCache.has(cleanMessage)) {\n    warnedOnceCache.add(cleanMessage);\n    if (gravity === 'error') {\n      console.error(cleanMessage);\n    } else {\n      console.warn(cleanMessage);\n    }\n  }\n}\nexport function clearWarningsCache() {\n  warnedOnceCache.clear();\n}", "map": {"version": 3, "names": ["warnedOnceCache", "Set", "warnOnce", "message", "gravity", "process", "env", "NODE_ENV", "cleanMessage", "Array", "isArray", "join", "has", "add", "console", "error", "warn", "clearWarningsCache", "clear"], "sources": ["D:/HeThongCongTyQuanLyNhanCong/Microservice_With_Kubernetes/microservice_fe/node_modules/@mui/x-internals/esm/warning/warning.js"], "sourcesContent": ["const warnedOnceCache = new Set();\n\n/**\n * Logs a message to the console on development mode. The warning will only be logged once.\n *\n * The message is the log's cache key. Two identical messages will only be logged once.\n *\n * This function is a no-op in production.\n *\n * @param message the message to log\n * @param gravity the gravity of the warning. Defaults to `'warning'`.\n * @returns\n */\nexport function warnOnce(message, gravity = 'warning') {\n  if (process.env.NODE_ENV === 'production') {\n    return;\n  }\n  const cleanMessage = Array.isArray(message) ? message.join('\\n') : message;\n  if (!warnedOnceCache.has(cleanMessage)) {\n    warnedOnceCache.add(cleanMessage);\n    if (gravity === 'error') {\n      console.error(cleanMessage);\n    } else {\n      console.warn(cleanMessage);\n    }\n  }\n}\nexport function clearWarningsCache() {\n  warnedOnceCache.clear();\n}"], "mappings": "AAAA,MAAMA,eAAe,GAAG,IAAIC,GAAG,CAAC,CAAC;;AAEjC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,QAAQA,CAACC,OAAO,EAAEC,OAAO,GAAG,SAAS,EAAE;EACrD,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzC;EACF;EACA,MAAMC,YAAY,GAAGC,KAAK,CAACC,OAAO,CAACP,OAAO,CAAC,GAAGA,OAAO,CAACQ,IAAI,CAAC,IAAI,CAAC,GAAGR,OAAO;EAC1E,IAAI,CAACH,eAAe,CAACY,GAAG,CAACJ,YAAY,CAAC,EAAE;IACtCR,eAAe,CAACa,GAAG,CAACL,YAAY,CAAC;IACjC,IAAIJ,OAAO,KAAK,OAAO,EAAE;MACvBU,OAAO,CAACC,KAAK,CAACP,YAAY,CAAC;IAC7B,CAAC,MAAM;MACLM,OAAO,CAACE,IAAI,CAACR,YAAY,CAAC;IAC5B;EACF;AACF;AACA,OAAO,SAASS,kBAAkBA,CAAA,EAAG;EACnCjB,eAAe,CAACkB,KAAK,CAAC,CAAC;AACzB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}