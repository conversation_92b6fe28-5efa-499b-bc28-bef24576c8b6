{"ast": null, "code": "import { buildMatchPatternFn } from \"../../_lib/buildMatchPatternFn.js\";\nimport { buildMatchFn } from \"../../_lib/buildMatchFn.js\";\nconst matchOrdinalNumberPattern = /^(\\d+)(º)?/i;\nconst parseOrdinalNumberPattern = /\\d+/i;\nconst matchEraPatterns = {\n  narrow: /^(ac|dc|a|d)/i,\n  abbreviated: /^(a\\.?\\s?c\\.?|a\\.?\\s?e\\.?\\s?c\\.?|d\\.?\\s?c\\.?|e\\.?\\s?c\\.?)/i,\n  wide: /^(antes de cristo|antes de la era com[uú]n|despu[eé]s de cristo|era com[uú]n)/i\n};\nconst parseEraPatterns = {\n  any: [/^ac/i, /^dc/i],\n  wide: [/^(antes de cristo|antes de la era com[uú]n)/i, /^(despu[eé]s de cristo|era com[uú]n)/i]\n};\nconst matchQuarterPatterns = {\n  narrow: /^[1234]/i,\n  abbreviated: /^T[1234]/i,\n  wide: /^[1234](º)? trimestre/i\n};\nconst parseQuarterPatterns = {\n  any: [/1/i, /2/i, /3/i, /4/i]\n};\nconst matchMonthPatterns = {\n  narrow: /^[efmajsond]/i,\n  abbreviated: /^(ene|feb|mar|abr|may|jun|jul|ago|sep|oct|nov|dic)/i,\n  wide: /^(enero|febrero|marzo|abril|mayo|junio|julio|agosto|septiembre|octubre|noviembre|diciembre)/i\n};\nconst parseMonthPatterns = {\n  narrow: [/^e/i, /^f/i, /^m/i, /^a/i, /^m/i, /^j/i, /^j/i, /^a/i, /^s/i, /^o/i, /^n/i, /^d/i],\n  any: [/^en/i, /^feb/i, /^mar/i, /^abr/i, /^may/i, /^jun/i, /^jul/i, /^ago/i, /^sep/i, /^oct/i, /^nov/i, /^dic/i]\n};\nconst matchDayPatterns = {\n  narrow: /^[dlmjvs]/i,\n  short: /^(do|lu|ma|mi|ju|vi|s[áa])/i,\n  abbreviated: /^(dom|lun|mar|mi[ée]|jue|vie|s[áa]b)/i,\n  wide: /^(domingo|lunes|martes|mi[ée]rcoles|jueves|viernes|s[áa]bado)/i\n};\nconst parseDayPatterns = {\n  narrow: [/^d/i, /^l/i, /^m/i, /^m/i, /^j/i, /^v/i, /^s/i],\n  any: [/^do/i, /^lu/i, /^ma/i, /^mi/i, /^ju/i, /^vi/i, /^sa/i]\n};\nconst matchDayPeriodPatterns = {\n  narrow: /^(a|p|mn|md|(de la|a las) (mañana|tarde|noche))/i,\n  any: /^([ap]\\.?\\s?m\\.?|medianoche|mediodia|(de la|a las) (mañana|tarde|noche))/i\n};\nconst parseDayPeriodPatterns = {\n  any: {\n    am: /^a/i,\n    pm: /^p/i,\n    midnight: /^mn/i,\n    noon: /^md/i,\n    morning: /mañana/i,\n    afternoon: /tarde/i,\n    evening: /tarde/i,\n    night: /noche/i\n  }\n};\nexport const match = {\n  ordinalNumber: buildMatchPatternFn({\n    matchPattern: matchOrdinalNumberPattern,\n    parsePattern: parseOrdinalNumberPattern,\n    valueCallback: function (value) {\n      return parseInt(value, 10);\n    }\n  }),\n  era: buildMatchFn({\n    matchPatterns: matchEraPatterns,\n    defaultMatchWidth: \"wide\",\n    parsePatterns: parseEraPatterns,\n    defaultParseWidth: \"any\"\n  }),\n  quarter: buildMatchFn({\n    matchPatterns: matchQuarterPatterns,\n    defaultMatchWidth: \"wide\",\n    parsePatterns: parseQuarterPatterns,\n    defaultParseWidth: \"any\",\n    valueCallback: index => index + 1\n  }),\n  month: buildMatchFn({\n    matchPatterns: matchMonthPatterns,\n    defaultMatchWidth: \"wide\",\n    parsePatterns: parseMonthPatterns,\n    defaultParseWidth: \"any\"\n  }),\n  day: buildMatchFn({\n    matchPatterns: matchDayPatterns,\n    defaultMatchWidth: \"wide\",\n    parsePatterns: parseDayPatterns,\n    defaultParseWidth: \"any\"\n  }),\n  dayPeriod: buildMatchFn({\n    matchPatterns: matchDayPeriodPatterns,\n    defaultMatchWidth: \"any\",\n    parsePatterns: parseDayPeriodPatterns,\n    defaultParseWidth: \"any\"\n  })\n};", "map": {"version": 3, "names": ["buildMatchPatternFn", "buildMatchFn", "matchOrdinalNumberPattern", "parseOrdinalNumberPattern", "matchEraPatterns", "narrow", "abbreviated", "wide", "parseEraPatterns", "any", "matchQuarterPatterns", "parseQuarterPatterns", "matchMonthPatterns", "parseMonthPatterns", "matchDayPatterns", "short", "parseDayPatterns", "matchDayPeriodPatterns", "parseDayPeriodPatterns", "am", "pm", "midnight", "noon", "morning", "afternoon", "evening", "night", "match", "ordinalNumber", "matchPattern", "parsePattern", "valueCallback", "value", "parseInt", "era", "matchPatterns", "defaultMatchWidth", "parsePatterns", "defaultParseWidth", "quarter", "index", "month", "day", "<PERSON><PERSON><PERSON><PERSON>"], "sources": ["D:/HeThongCongTyQuanLyNhanCong/Microservice_With_Kubernetes/microservice_fe/node_modules/date-fns/locale/es/_lib/match.js"], "sourcesContent": ["import { buildMatchPatternFn } from \"../../_lib/buildMatchPatternFn.js\";\nimport { buildMatchFn } from \"../../_lib/buildMatchFn.js\";\n\nconst matchOrdinalNumberPattern = /^(\\d+)(º)?/i;\nconst parseOrdinalNumberPattern = /\\d+/i;\n\nconst matchEraPatterns = {\n  narrow: /^(ac|dc|a|d)/i,\n  abbreviated: /^(a\\.?\\s?c\\.?|a\\.?\\s?e\\.?\\s?c\\.?|d\\.?\\s?c\\.?|e\\.?\\s?c\\.?)/i,\n  wide: /^(antes de cristo|antes de la era com[uú]n|despu[eé]s de cristo|era com[uú]n)/i,\n};\nconst parseEraPatterns = {\n  any: [/^ac/i, /^dc/i],\n  wide: [\n    /^(antes de cristo|antes de la era com[uú]n)/i,\n    /^(despu[eé]s de cristo|era com[uú]n)/i,\n  ],\n};\n\nconst matchQuarterPatterns = {\n  narrow: /^[1234]/i,\n  abbreviated: /^T[1234]/i,\n  wide: /^[1234](º)? trimestre/i,\n};\nconst parseQuarterPatterns = {\n  any: [/1/i, /2/i, /3/i, /4/i],\n};\n\nconst matchMonthPatterns = {\n  narrow: /^[efmajsond]/i,\n  abbreviated: /^(ene|feb|mar|abr|may|jun|jul|ago|sep|oct|nov|dic)/i,\n  wide: /^(enero|febrero|marzo|abril|mayo|junio|julio|agosto|septiembre|octubre|noviembre|diciembre)/i,\n};\nconst parseMonthPatterns = {\n  narrow: [\n    /^e/i,\n    /^f/i,\n    /^m/i,\n    /^a/i,\n    /^m/i,\n    /^j/i,\n    /^j/i,\n    /^a/i,\n    /^s/i,\n    /^o/i,\n    /^n/i,\n    /^d/i,\n  ],\n\n  any: [\n    /^en/i,\n    /^feb/i,\n    /^mar/i,\n    /^abr/i,\n    /^may/i,\n    /^jun/i,\n    /^jul/i,\n    /^ago/i,\n    /^sep/i,\n    /^oct/i,\n    /^nov/i,\n    /^dic/i,\n  ],\n};\n\nconst matchDayPatterns = {\n  narrow: /^[dlmjvs]/i,\n  short: /^(do|lu|ma|mi|ju|vi|s[áa])/i,\n  abbreviated: /^(dom|lun|mar|mi[ée]|jue|vie|s[áa]b)/i,\n  wide: /^(domingo|lunes|martes|mi[ée]rcoles|jueves|viernes|s[áa]bado)/i,\n};\nconst parseDayPatterns = {\n  narrow: [/^d/i, /^l/i, /^m/i, /^m/i, /^j/i, /^v/i, /^s/i],\n  any: [/^do/i, /^lu/i, /^ma/i, /^mi/i, /^ju/i, /^vi/i, /^sa/i],\n};\n\nconst matchDayPeriodPatterns = {\n  narrow: /^(a|p|mn|md|(de la|a las) (mañana|tarde|noche))/i,\n  any: /^([ap]\\.?\\s?m\\.?|medianoche|mediodia|(de la|a las) (mañana|tarde|noche))/i,\n};\nconst parseDayPeriodPatterns = {\n  any: {\n    am: /^a/i,\n    pm: /^p/i,\n    midnight: /^mn/i,\n    noon: /^md/i,\n    morning: /mañana/i,\n    afternoon: /tarde/i,\n    evening: /tarde/i,\n    night: /noche/i,\n  },\n};\n\nexport const match = {\n  ordinalNumber: buildMatchPatternFn({\n    matchPattern: matchOrdinalNumberPattern,\n    parsePattern: parseOrdinalNumberPattern,\n    valueCallback: function (value) {\n      return parseInt(value, 10);\n    },\n  }),\n\n  era: buildMatchFn({\n    matchPatterns: matchEraPatterns,\n    defaultMatchWidth: \"wide\",\n    parsePatterns: parseEraPatterns,\n    defaultParseWidth: \"any\",\n  }),\n\n  quarter: buildMatchFn({\n    matchPatterns: matchQuarterPatterns,\n    defaultMatchWidth: \"wide\",\n    parsePatterns: parseQuarterPatterns,\n    defaultParseWidth: \"any\",\n    valueCallback: (index) => index + 1,\n  }),\n\n  month: buildMatchFn({\n    matchPatterns: matchMonthPatterns,\n    defaultMatchWidth: \"wide\",\n    parsePatterns: parseMonthPatterns,\n    defaultParseWidth: \"any\",\n  }),\n\n  day: buildMatchFn({\n    matchPatterns: matchDayPatterns,\n    defaultMatchWidth: \"wide\",\n    parsePatterns: parseDayPatterns,\n    defaultParseWidth: \"any\",\n  }),\n\n  dayPeriod: buildMatchFn({\n    matchPatterns: matchDayPeriodPatterns,\n    defaultMatchWidth: \"any\",\n    parsePatterns: parseDayPeriodPatterns,\n    defaultParseWidth: \"any\",\n  }),\n};\n"], "mappings": "AAAA,SAASA,mBAAmB,QAAQ,mCAAmC;AACvE,SAASC,YAAY,QAAQ,4BAA4B;AAEzD,MAAMC,yBAAyB,GAAG,aAAa;AAC/C,MAAMC,yBAAyB,GAAG,MAAM;AAExC,MAAMC,gBAAgB,GAAG;EACvBC,MAAM,EAAE,eAAe;EACvBC,WAAW,EAAE,4DAA4D;EACzEC,IAAI,EAAE;AACR,CAAC;AACD,MAAMC,gBAAgB,GAAG;EACvBC,GAAG,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC;EACrBF,IAAI,EAAE,CACJ,8CAA8C,EAC9C,uCAAuC;AAE3C,CAAC;AAED,MAAMG,oBAAoB,GAAG;EAC3BL,MAAM,EAAE,UAAU;EAClBC,WAAW,EAAE,WAAW;EACxBC,IAAI,EAAE;AACR,CAAC;AACD,MAAMI,oBAAoB,GAAG;EAC3BF,GAAG,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI;AAC9B,CAAC;AAED,MAAMG,kBAAkB,GAAG;EACzBP,MAAM,EAAE,eAAe;EACvBC,WAAW,EAAE,qDAAqD;EAClEC,IAAI,EAAE;AACR,CAAC;AACD,MAAMM,kBAAkB,GAAG;EACzBR,MAAM,EAAE,CACN,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,CACN;EAEDI,GAAG,EAAE,CACH,MAAM,EACN,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO;AAEX,CAAC;AAED,MAAMK,gBAAgB,GAAG;EACvBT,MAAM,EAAE,YAAY;EACpBU,KAAK,EAAE,6BAA6B;EACpCT,WAAW,EAAE,uCAAuC;EACpDC,IAAI,EAAE;AACR,CAAC;AACD,MAAMS,gBAAgB,GAAG;EACvBX,MAAM,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;EACzDI,GAAG,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM;AAC9D,CAAC;AAED,MAAMQ,sBAAsB,GAAG;EAC7BZ,MAAM,EAAE,kDAAkD;EAC1DI,GAAG,EAAE;AACP,CAAC;AACD,MAAMS,sBAAsB,GAAG;EAC7BT,GAAG,EAAE;IACHU,EAAE,EAAE,KAAK;IACTC,EAAE,EAAE,KAAK;IACTC,QAAQ,EAAE,MAAM;IAChBC,IAAI,EAAE,MAAM;IACZC,OAAO,EAAE,SAAS;IAClBC,SAAS,EAAE,QAAQ;IACnBC,OAAO,EAAE,QAAQ;IACjBC,KAAK,EAAE;EACT;AACF,CAAC;AAED,OAAO,MAAMC,KAAK,GAAG;EACnBC,aAAa,EAAE5B,mBAAmB,CAAC;IACjC6B,YAAY,EAAE3B,yBAAyB;IACvC4B,YAAY,EAAE3B,yBAAyB;IACvC4B,aAAa,EAAE,SAAAA,CAAUC,KAAK,EAAE;MAC9B,OAAOC,QAAQ,CAACD,KAAK,EAAE,EAAE,CAAC;IAC5B;EACF,CAAC,CAAC;EAEFE,GAAG,EAAEjC,YAAY,CAAC;IAChBkC,aAAa,EAAE/B,gBAAgB;IAC/BgC,iBAAiB,EAAE,MAAM;IACzBC,aAAa,EAAE7B,gBAAgB;IAC/B8B,iBAAiB,EAAE;EACrB,CAAC,CAAC;EAEFC,OAAO,EAAEtC,YAAY,CAAC;IACpBkC,aAAa,EAAEzB,oBAAoB;IACnC0B,iBAAiB,EAAE,MAAM;IACzBC,aAAa,EAAE1B,oBAAoB;IACnC2B,iBAAiB,EAAE,KAAK;IACxBP,aAAa,EAAGS,KAAK,IAAKA,KAAK,GAAG;EACpC,CAAC,CAAC;EAEFC,KAAK,EAAExC,YAAY,CAAC;IAClBkC,aAAa,EAAEvB,kBAAkB;IACjCwB,iBAAiB,EAAE,MAAM;IACzBC,aAAa,EAAExB,kBAAkB;IACjCyB,iBAAiB,EAAE;EACrB,CAAC,CAAC;EAEFI,GAAG,EAAEzC,YAAY,CAAC;IAChBkC,aAAa,EAAErB,gBAAgB;IAC/BsB,iBAAiB,EAAE,MAAM;IACzBC,aAAa,EAAErB,gBAAgB;IAC/BsB,iBAAiB,EAAE;EACrB,CAAC,CAAC;EAEFK,SAAS,EAAE1C,YAAY,CAAC;IACtBkC,aAAa,EAAElB,sBAAsB;IACrCmB,iBAAiB,EAAE,KAAK;IACxBC,aAAa,EAAEnB,sBAAsB;IACrCoB,iBAAiB,EAAE;EACrB,CAAC;AACH,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}