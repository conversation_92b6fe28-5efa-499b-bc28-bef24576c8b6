{"ast": null, "code": "const formatDistanceLocale = {\n  lessThanXSeconds: {\n    one: \"llai na eiliad\",\n    other: \"llai na {{count}} eiliad\"\n  },\n  xSeconds: {\n    one: \"1 eiliad\",\n    other: \"{{count}} eiliad\"\n  },\n  halfAMinute: \"hanner munud\",\n  lessThanXMinutes: {\n    one: \"llai na munud\",\n    two: \"llai na 2 funud\",\n    other: \"llai na {{count}} munud\"\n  },\n  xMinutes: {\n    one: \"1 munud\",\n    two: \"2 funud\",\n    other: \"{{count}} munud\"\n  },\n  aboutXHours: {\n    one: \"tua 1 awr\",\n    other: \"tua {{count}} awr\"\n  },\n  xHours: {\n    one: \"1 awr\",\n    other: \"{{count}} awr\"\n  },\n  xDays: {\n    one: \"1 diwrnod\",\n    two: \"2 ddiwrnod\",\n    other: \"{{count}} diwrnod\"\n  },\n  aboutXWeeks: {\n    one: \"tua 1 wythnos\",\n    two: \"tua pythefnos\",\n    other: \"tua {{count}} wythnos\"\n  },\n  xWeeks: {\n    one: \"1 wythnos\",\n    two: \"pythefnos\",\n    other: \"{{count}} wythnos\"\n  },\n  aboutXMonths: {\n    one: \"tua 1 mis\",\n    two: \"tua 2 fis\",\n    other: \"tua {{count}} mis\"\n  },\n  xMonths: {\n    one: \"1 mis\",\n    two: \"2 fis\",\n    other: \"{{count}} mis\"\n  },\n  aboutXYears: {\n    one: \"tua 1 flwyddyn\",\n    two: \"tua 2 flynedd\",\n    other: \"tua {{count}} mlynedd\"\n  },\n  xYears: {\n    one: \"1 flwyddyn\",\n    two: \"2 flynedd\",\n    other: \"{{count}} mlynedd\"\n  },\n  overXYears: {\n    one: \"dros 1 flwyddyn\",\n    two: \"dros 2 flynedd\",\n    other: \"dros {{count}} mlynedd\"\n  },\n  almostXYears: {\n    one: \"bron 1 flwyddyn\",\n    two: \"bron 2 flynedd\",\n    other: \"bron {{count}} mlynedd\"\n  }\n};\nexport const formatDistance = (token, count, options) => {\n  let result;\n  const tokenValue = formatDistanceLocale[token];\n  if (typeof tokenValue === \"string\") {\n    result = tokenValue;\n  } else if (count === 1) {\n    result = tokenValue.one;\n  } else if (count === 2 && !!tokenValue.two) {\n    result = tokenValue.two;\n  } else {\n    result = tokenValue.other.replace(\"{{count}}\", String(count));\n  }\n  if (options !== null && options !== void 0 && options.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      return \"mewn \" + result;\n    } else {\n      return result + \" yn ôl\";\n    }\n  }\n  return result;\n};", "map": {"version": 3, "names": ["formatDistanceLocale", "lessThanXSeconds", "one", "other", "xSeconds", "halfAMinute", "lessThanXMinutes", "two", "xMinutes", "aboutXHours", "xHours", "xDays", "aboutXWeeks", "xWeeks", "aboutXMonths", "xMonths", "aboutXYears", "xYears", "overXYears", "almostXYears", "formatDistance", "token", "count", "options", "result", "tokenValue", "replace", "String", "addSuffix", "comparison"], "sources": ["D:/HeThongCongTyQuanLyNhanCong/Microservice_With_Kubernetes/microservice_fe/node_modules/date-fns/locale/cy/_lib/formatDistance.js"], "sourcesContent": ["const formatDistanceLocale = {\n  lessThanXSeconds: {\n    one: \"llai na eiliad\",\n    other: \"llai na {{count}} eiliad\",\n  },\n\n  xSeconds: {\n    one: \"1 eiliad\",\n    other: \"{{count}} eiliad\",\n  },\n\n  halfAMinute: \"hanner munud\",\n\n  lessThanXMinutes: {\n    one: \"llai na munud\",\n    two: \"llai na 2 funud\",\n    other: \"llai na {{count}} munud\",\n  },\n\n  xMinutes: {\n    one: \"1 munud\",\n    two: \"2 funud\",\n    other: \"{{count}} munud\",\n  },\n\n  aboutXHours: {\n    one: \"tua 1 awr\",\n    other: \"tua {{count}} awr\",\n  },\n\n  xHours: {\n    one: \"1 awr\",\n    other: \"{{count}} awr\",\n  },\n\n  xDays: {\n    one: \"1 diwrnod\",\n    two: \"2 ddiwrnod\",\n    other: \"{{count}} diwrnod\",\n  },\n\n  aboutXWeeks: {\n    one: \"tua 1 wythnos\",\n    two: \"tua pythefnos\",\n    other: \"tua {{count}} wythnos\",\n  },\n\n  xWeeks: {\n    one: \"1 wythnos\",\n    two: \"pythefnos\",\n    other: \"{{count}} wythnos\",\n  },\n\n  aboutXMonths: {\n    one: \"tua 1 mis\",\n    two: \"tua 2 fis\",\n    other: \"tua {{count}} mis\",\n  },\n\n  xMonths: {\n    one: \"1 mis\",\n    two: \"2 fis\",\n    other: \"{{count}} mis\",\n  },\n\n  aboutXYears: {\n    one: \"tua 1 flwyddyn\",\n    two: \"tua 2 flynedd\",\n    other: \"tua {{count}} mlynedd\",\n  },\n\n  xYears: {\n    one: \"1 flwyddyn\",\n    two: \"2 flynedd\",\n    other: \"{{count}} mlynedd\",\n  },\n\n  overXYears: {\n    one: \"dros 1 flwyddyn\",\n    two: \"dros 2 flynedd\",\n    other: \"dros {{count}} mlynedd\",\n  },\n\n  almostXYears: {\n    one: \"bron 1 flwyddyn\",\n    two: \"bron 2 flynedd\",\n    other: \"bron {{count}} mlynedd\",\n  },\n};\n\nexport const formatDistance = (token, count, options) => {\n  let result;\n\n  const tokenValue = formatDistanceLocale[token];\n  if (typeof tokenValue === \"string\") {\n    result = tokenValue;\n  } else if (count === 1) {\n    result = tokenValue.one;\n  } else if (count === 2 && !!tokenValue.two) {\n    result = tokenValue.two;\n  } else {\n    result = tokenValue.other.replace(\"{{count}}\", String(count));\n  }\n\n  if (options?.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      return \"mewn \" + result;\n    } else {\n      return result + \" yn ôl\";\n    }\n  }\n\n  return result;\n};\n"], "mappings": "AAAA,MAAMA,oBAAoB,GAAG;EAC3BC,gBAAgB,EAAE;IAChBC,GAAG,EAAE,gBAAgB;IACrBC,KAAK,EAAE;EACT,CAAC;EAEDC,QAAQ,EAAE;IACRF,GAAG,EAAE,UAAU;IACfC,KAAK,EAAE;EACT,CAAC;EAEDE,WAAW,EAAE,cAAc;EAE3BC,gBAAgB,EAAE;IAChBJ,GAAG,EAAE,eAAe;IACpBK,GAAG,EAAE,iBAAiB;IACtBJ,KAAK,EAAE;EACT,CAAC;EAEDK,QAAQ,EAAE;IACRN,GAAG,EAAE,SAAS;IACdK,GAAG,EAAE,SAAS;IACdJ,KAAK,EAAE;EACT,CAAC;EAEDM,WAAW,EAAE;IACXP,GAAG,EAAE,WAAW;IAChBC,KAAK,EAAE;EACT,CAAC;EAEDO,MAAM,EAAE;IACNR,GAAG,EAAE,OAAO;IACZC,KAAK,EAAE;EACT,CAAC;EAEDQ,KAAK,EAAE;IACLT,GAAG,EAAE,WAAW;IAChBK,GAAG,EAAE,YAAY;IACjBJ,KAAK,EAAE;EACT,CAAC;EAEDS,WAAW,EAAE;IACXV,GAAG,EAAE,eAAe;IACpBK,GAAG,EAAE,eAAe;IACpBJ,KAAK,EAAE;EACT,CAAC;EAEDU,MAAM,EAAE;IACNX,GAAG,EAAE,WAAW;IAChBK,GAAG,EAAE,WAAW;IAChBJ,KAAK,EAAE;EACT,CAAC;EAEDW,YAAY,EAAE;IACZZ,GAAG,EAAE,WAAW;IAChBK,GAAG,EAAE,WAAW;IAChBJ,KAAK,EAAE;EACT,CAAC;EAEDY,OAAO,EAAE;IACPb,GAAG,EAAE,OAAO;IACZK,GAAG,EAAE,OAAO;IACZJ,KAAK,EAAE;EACT,CAAC;EAEDa,WAAW,EAAE;IACXd,GAAG,EAAE,gBAAgB;IACrBK,GAAG,EAAE,eAAe;IACpBJ,KAAK,EAAE;EACT,CAAC;EAEDc,MAAM,EAAE;IACNf,GAAG,EAAE,YAAY;IACjBK,GAAG,EAAE,WAAW;IAChBJ,KAAK,EAAE;EACT,CAAC;EAEDe,UAAU,EAAE;IACVhB,GAAG,EAAE,iBAAiB;IACtBK,GAAG,EAAE,gBAAgB;IACrBJ,KAAK,EAAE;EACT,CAAC;EAEDgB,YAAY,EAAE;IACZjB,GAAG,EAAE,iBAAiB;IACtBK,GAAG,EAAE,gBAAgB;IACrBJ,KAAK,EAAE;EACT;AACF,CAAC;AAED,OAAO,MAAMiB,cAAc,GAAGA,CAACC,KAAK,EAAEC,KAAK,EAAEC,OAAO,KAAK;EACvD,IAAIC,MAAM;EAEV,MAAMC,UAAU,GAAGzB,oBAAoB,CAACqB,KAAK,CAAC;EAC9C,IAAI,OAAOI,UAAU,KAAK,QAAQ,EAAE;IAClCD,MAAM,GAAGC,UAAU;EACrB,CAAC,MAAM,IAAIH,KAAK,KAAK,CAAC,EAAE;IACtBE,MAAM,GAAGC,UAAU,CAACvB,GAAG;EACzB,CAAC,MAAM,IAAIoB,KAAK,KAAK,CAAC,IAAI,CAAC,CAACG,UAAU,CAAClB,GAAG,EAAE;IAC1CiB,MAAM,GAAGC,UAAU,CAAClB,GAAG;EACzB,CAAC,MAAM;IACLiB,MAAM,GAAGC,UAAU,CAACtB,KAAK,CAACuB,OAAO,CAAC,WAAW,EAAEC,MAAM,CAACL,KAAK,CAAC,CAAC;EAC/D;EAEA,IAAIC,OAAO,aAAPA,OAAO,eAAPA,OAAO,CAAEK,SAAS,EAAE;IACtB,IAAIL,OAAO,CAACM,UAAU,IAAIN,OAAO,CAACM,UAAU,GAAG,CAAC,EAAE;MAChD,OAAO,OAAO,GAAGL,MAAM;IACzB,CAAC,MAAM;MACL,OAAOA,MAAM,GAAG,QAAQ;IAC1B;EACF;EAEA,OAAOA,MAAM;AACf,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}