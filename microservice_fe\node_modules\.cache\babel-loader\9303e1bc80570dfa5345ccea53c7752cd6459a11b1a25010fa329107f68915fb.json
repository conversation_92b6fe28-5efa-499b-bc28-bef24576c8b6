{"ast": null, "code": "const formatRelativeLocale = {\n  lastWeek: \"eeee 'گذشته در' p\",\n  yesterday: \"'دیروز در' p\",\n  today: \"'امروز در' p\",\n  tomorrow: \"'فردا در' p\",\n  nextWeek: \"eeee 'در' p\",\n  other: \"P\"\n};\nexport const formatRelative = (token, _date, _baseDate, _options) => formatRelativeLocale[token];", "map": {"version": 3, "names": ["formatRelativeLocale", "lastWeek", "yesterday", "today", "tomorrow", "nextWeek", "other", "formatRelative", "token", "_date", "_baseDate", "_options"], "sources": ["D:/HeThongCongTyQuanLyNhanCong/Microservice_With_Kubernetes/microservice_fe/node_modules/date-fns/locale/fa-IR/_lib/formatRelative.js"], "sourcesContent": ["const formatRelativeLocale = {\n  lastWeek: \"eeee 'گذشته در' p\",\n  yesterday: \"'دیروز در' p\",\n  today: \"'امروز در' p\",\n  tomorrow: \"'فردا در' p\",\n  nextWeek: \"eeee 'در' p\",\n  other: \"P\",\n};\n\nexport const formatRelative = (token, _date, _baseDate, _options) =>\n  formatRelativeLocale[token];\n"], "mappings": "AAAA,MAAMA,oBAAoB,GAAG;EAC3BC,QAAQ,EAAE,mBAAmB;EAC7BC,SAAS,EAAE,cAAc;EACzBC,KAAK,EAAE,cAAc;EACrBC,QAAQ,EAAE,aAAa;EACvBC,QAAQ,EAAE,aAAa;EACvBC,KAAK,EAAE;AACT,CAAC;AAED,OAAO,MAAMC,cAAc,GAAGA,CAACC,KAAK,EAAEC,KAAK,EAAEC,SAAS,EAAEC,QAAQ,KAC9DX,oBAAoB,CAACQ,KAAK,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}