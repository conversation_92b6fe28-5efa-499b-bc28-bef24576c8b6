{"ast": null, "code": "// Format currency in VND\nexport const formatCurrency = value => {\n  return new Intl.NumberFormat('vi-VN', {\n    style: 'currency',\n    currency: 'VND',\n    minimumFractionDigits: 0,\n    maximumFractionDigits: 0\n  }).format(value);\n};\n\n// Format date to Vietnamese format (DD/MM/YYYY)\nexport const formatDate = dateString => {\n  try {\n    const date = new Date(dateString);\n    if (isNaN(date.getTime())) {\n      return dateString;\n    }\n    const day = String(date.getDate()).padStart(2, '0');\n    const month = String(date.getMonth() + 1).padStart(2, '0');\n    const year = date.getFullYear();\n    return `${day}/${month}/${year}`;\n  } catch (error) {\n    return dateString;\n  }\n};\n\n// Format date and time to Vietnamese format (HH:MM DD/MM/YYYY)\nexport const formatDateTime = dateString => {\n  try {\n    const date = new Date(dateString);\n    if (isNaN(date.getTime())) {\n      return dateString;\n    }\n    const hours = String(date.getHours()).padStart(2, '0');\n    const minutes = String(date.getMinutes()).padStart(2, '0');\n    const day = String(date.getDate()).padStart(2, '0');\n    const month = String(date.getMonth() + 1).padStart(2, '0');\n    const year = date.getFullYear();\n    return `${hours}:${minutes} ${day}/${month}/${year}`;\n  } catch (error) {\n    return dateString;\n  }\n};", "map": {"version": 3, "names": ["formatCurrency", "value", "Intl", "NumberFormat", "style", "currency", "minimumFractionDigits", "maximumFractionDigits", "format", "formatDate", "dateString", "date", "Date", "isNaN", "getTime", "day", "String", "getDate", "padStart", "month", "getMonth", "year", "getFullYear", "error", "formatDateTime", "hours", "getHours", "minutes", "getMinutes"], "sources": ["D:/HeThongCongTyQuanLyNhanCong/Microservice_With_Kubernetes/microservice_fe/src/utils/formatters.ts"], "sourcesContent": ["// Format currency in VND\nexport const formatCurrency = (value: number): string => {\n  return new Intl.NumberFormat('vi-VN', {\n    style: 'currency',\n    currency: 'VND',\n    minimumFractionDigits: 0,\n    maximumFractionDigits: 0,\n  }).format(value);\n};\n\n// Format date to Vietnamese format (DD/MM/YYYY)\nexport const formatDate = (dateString: string): string => {\n  try {\n    const date = new Date(dateString);\n    if (isNaN(date.getTime())) {\n      return dateString;\n    }\n    const day = String(date.getDate()).padStart(2, '0');\n    const month = String(date.getMonth() + 1).padStart(2, '0');\n    const year = date.getFullYear();\n    return `${day}/${month}/${year}`;\n  } catch (error) {\n    return dateString;\n  }\n};\n\n// Format date and time to Vietnamese format (HH:MM DD/MM/YYYY)\nexport const formatDateTime = (dateString: string): string => {\n  try {\n    const date = new Date(dateString);\n    if (isNaN(date.getTime())) {\n      return dateString;\n    }\n    const hours = String(date.getHours()).padStart(2, '0');\n    const minutes = String(date.getMinutes()).padStart(2, '0');\n    const day = String(date.getDate()).padStart(2, '0');\n    const month = String(date.getMonth() + 1).padStart(2, '0');\n    const year = date.getFullYear();\n    return `${hours}:${minutes} ${day}/${month}/${year}`;\n  } catch (error) {\n    return dateString;\n  }\n};\n"], "mappings": "AAAA;AACA,OAAO,MAAMA,cAAc,GAAIC,KAAa,IAAa;EACvD,OAAO,IAAIC,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;IACpCC,KAAK,EAAE,UAAU;IACjBC,QAAQ,EAAE,KAAK;IACfC,qBAAqB,EAAE,CAAC;IACxBC,qBAAqB,EAAE;EACzB,CAAC,CAAC,CAACC,MAAM,CAACP,KAAK,CAAC;AAClB,CAAC;;AAED;AACA,OAAO,MAAMQ,UAAU,GAAIC,UAAkB,IAAa;EACxD,IAAI;IACF,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAACF,UAAU,CAAC;IACjC,IAAIG,KAAK,CAACF,IAAI,CAACG,OAAO,CAAC,CAAC,CAAC,EAAE;MACzB,OAAOJ,UAAU;IACnB;IACA,MAAMK,GAAG,GAAGC,MAAM,CAACL,IAAI,CAACM,OAAO,CAAC,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;IACnD,MAAMC,KAAK,GAAGH,MAAM,CAACL,IAAI,CAACS,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC,CAACF,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;IAC1D,MAAMG,IAAI,GAAGV,IAAI,CAACW,WAAW,CAAC,CAAC;IAC/B,OAAO,GAAGP,GAAG,IAAII,KAAK,IAAIE,IAAI,EAAE;EAClC,CAAC,CAAC,OAAOE,KAAK,EAAE;IACd,OAAOb,UAAU;EACnB;AACF,CAAC;;AAED;AACA,OAAO,MAAMc,cAAc,GAAId,UAAkB,IAAa;EAC5D,IAAI;IACF,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAACF,UAAU,CAAC;IACjC,IAAIG,KAAK,CAACF,IAAI,CAACG,OAAO,CAAC,CAAC,CAAC,EAAE;MACzB,OAAOJ,UAAU;IACnB;IACA,MAAMe,KAAK,GAAGT,MAAM,CAACL,IAAI,CAACe,QAAQ,CAAC,CAAC,CAAC,CAACR,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;IACtD,MAAMS,OAAO,GAAGX,MAAM,CAACL,IAAI,CAACiB,UAAU,CAAC,CAAC,CAAC,CAACV,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;IAC1D,MAAMH,GAAG,GAAGC,MAAM,CAACL,IAAI,CAACM,OAAO,CAAC,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;IACnD,MAAMC,KAAK,GAAGH,MAAM,CAACL,IAAI,CAACS,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC,CAACF,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;IAC1D,MAAMG,IAAI,GAAGV,IAAI,CAACW,WAAW,CAAC,CAAC;IAC/B,OAAO,GAAGG,KAAK,IAAIE,OAAO,IAAIZ,GAAG,IAAII,KAAK,IAAIE,IAAI,EAAE;EACtD,CAAC,CAAC,OAAOE,KAAK,EAAE;IACd,OAAOb,UAAU;EACnB;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}