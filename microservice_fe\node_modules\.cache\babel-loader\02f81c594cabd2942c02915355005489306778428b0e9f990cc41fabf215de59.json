{"ast": null, "code": "import { formatDistance } from \"./sv/_lib/formatDistance.js\";\nimport { formatLong } from \"./sv/_lib/formatLong.js\";\nimport { formatRelative } from \"./sv/_lib/formatRelative.js\";\nimport { localize } from \"./sv/_lib/localize.js\";\nimport { match } from \"./sv/_lib/match.js\";\n\n/**\n * @category Locales\n * @summary Swedish locale.\n * @language Swedish\n * @iso-639-2 swe\n * <AUTHOR> [@ejulen](https://github.com/ejulen)\n * <AUTHOR> [@alexandernanberg](https://github.com/alexandernanberg)\n * <AUTHOR> [@limelights](https://github.com/limelights)\n */\nexport const sv = {\n  code: \"sv\",\n  formatDistance: formatDistance,\n  formatLong: formatLong,\n  formatRelative: formatRelative,\n  localize: localize,\n  match: match,\n  options: {\n    weekStartsOn: 1 /* Monday */,\n    firstWeekContainsDate: 4\n  }\n};\n\n// Fallback for modularized imports:\nexport default sv;", "map": {"version": 3, "names": ["formatDistance", "formatLong", "formatRelative", "localize", "match", "sv", "code", "options", "weekStartsOn", "firstWeekContainsDate"], "sources": ["D:/HeThongCongTyQuanLyNhanCong/Microservice_With_Kubernetes/microservice_fe/node_modules/date-fns/locale/sv.js"], "sourcesContent": ["import { formatDistance } from \"./sv/_lib/formatDistance.js\";\nimport { formatLong } from \"./sv/_lib/formatLong.js\";\nimport { formatRelative } from \"./sv/_lib/formatRelative.js\";\nimport { localize } from \"./sv/_lib/localize.js\";\nimport { match } from \"./sv/_lib/match.js\";\n\n/**\n * @category Locales\n * @summary Swedish locale.\n * @language Swedish\n * @iso-639-2 swe\n * <AUTHOR> [@ejulen](https://github.com/ejulen)\n * <AUTHOR> [@alexandernanberg](https://github.com/alexandernanberg)\n * <AUTHOR> [@limelights](https://github.com/limelights)\n */\nexport const sv = {\n  code: \"sv\",\n  formatDistance: formatDistance,\n  formatLong: formatLong,\n  formatRelative: formatRelative,\n  localize: localize,\n  match: match,\n  options: {\n    weekStartsOn: 1 /* Monday */,\n    firstWeekContainsDate: 4,\n  },\n};\n\n// Fallback for modularized imports:\nexport default sv;\n"], "mappings": "AAAA,SAASA,cAAc,QAAQ,6BAA6B;AAC5D,SAASC,UAAU,QAAQ,yBAAyB;AACpD,SAASC,cAAc,QAAQ,6BAA6B;AAC5D,SAASC,QAAQ,QAAQ,uBAAuB;AAChD,SAASC,KAAK,QAAQ,oBAAoB;;AAE1C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,EAAE,GAAG;EAChBC,IAAI,EAAE,IAAI;EACVN,cAAc,EAAEA,cAAc;EAC9BC,UAAU,EAAEA,UAAU;EACtBC,cAAc,EAAEA,cAAc;EAC9BC,QAAQ,EAAEA,QAAQ;EAClBC,KAAK,EAAEA,KAAK;EACZG,OAAO,EAAE;IACPC,YAAY,EAAE,CAAC,CAAC;IAChBC,qBAAqB,EAAE;EACzB;AACF,CAAC;;AAED;AACA,eAAeJ,EAAE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}