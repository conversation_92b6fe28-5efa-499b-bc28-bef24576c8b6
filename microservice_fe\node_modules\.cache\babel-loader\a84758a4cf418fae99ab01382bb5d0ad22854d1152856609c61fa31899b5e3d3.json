{"ast": null, "code": "import { getActiveElement } from \"../../utils/utils.js\";\nexport function syncSelectionToDOM(parameters) {\n  const {\n    focused,\n    domGetters,\n    stateResponse: {\n      // States and derived states\n      parsedSelectedSections,\n      state\n    }\n  } = parameters;\n  if (!domGetters.isReady()) {\n    return;\n  }\n  const selection = document.getSelection();\n  if (!selection) {\n    return;\n  }\n  if (parsedSelectedSections == null) {\n    // If the selection contains an element inside the field, we reset it.\n    if (selection.rangeCount > 0 && domGetters.getRoot().contains(selection.getRangeAt(0).startContainer)) {\n      selection.removeAllRanges();\n    }\n    if (focused) {\n      domGetters.getRoot().blur();\n    }\n    return;\n  }\n\n  // On multi input range pickers we want to update selection range only for the active input\n  if (!domGetters.getRoot().contains(getActiveElement(document))) {\n    return;\n  }\n  const range = new window.Range();\n  let target;\n  if (parsedSelectedSections === 'all') {\n    target = domGetters.getRoot();\n  } else {\n    const section = state.sections[parsedSelectedSections];\n    if (section.type === 'empty') {\n      target = domGetters.getSectionContainer(parsedSelectedSections);\n    } else {\n      target = domGetters.getSectionContent(parsedSelectedSections);\n    }\n  }\n  range.selectNodeContents(target);\n  target.focus();\n  selection.removeAllRanges();\n  selection.addRange(range);\n}", "map": {"version": 3, "names": ["getActiveElement", "syncSelectionToDOM", "parameters", "focused", "domGetters", "stateResponse", "parsedSelectedSections", "state", "isReady", "selection", "document", "getSelection", "rangeCount", "getRoot", "contains", "getRangeAt", "startContainer", "removeAllRanges", "blur", "range", "window", "Range", "target", "section", "sections", "type", "getSectionContainer", "getSectionContent", "selectNodeContents", "focus", "addRange"], "sources": ["D:/HeThongCongTyQuanLyNhanCong/Microservice_With_Kubernetes/microservice_fe/node_modules/@mui/x-date-pickers/esm/internals/hooks/useField/syncSelectionToDOM.js"], "sourcesContent": ["import { getActiveElement } from \"../../utils/utils.js\";\nexport function syncSelectionToDOM(parameters) {\n  const {\n    focused,\n    domGetters,\n    stateResponse: {\n      // States and derived states\n      parsedSelectedSections,\n      state\n    }\n  } = parameters;\n  if (!domGetters.isReady()) {\n    return;\n  }\n  const selection = document.getSelection();\n  if (!selection) {\n    return;\n  }\n  if (parsedSelectedSections == null) {\n    // If the selection contains an element inside the field, we reset it.\n    if (selection.rangeCount > 0 && domGetters.getRoot().contains(selection.getRangeAt(0).startContainer)) {\n      selection.removeAllRanges();\n    }\n    if (focused) {\n      domGetters.getRoot().blur();\n    }\n    return;\n  }\n\n  // On multi input range pickers we want to update selection range only for the active input\n  if (!domGetters.getRoot().contains(getActiveElement(document))) {\n    return;\n  }\n  const range = new window.Range();\n  let target;\n  if (parsedSelectedSections === 'all') {\n    target = domGetters.getRoot();\n  } else {\n    const section = state.sections[parsedSelectedSections];\n    if (section.type === 'empty') {\n      target = domGetters.getSectionContainer(parsedSelectedSections);\n    } else {\n      target = domGetters.getSectionContent(parsedSelectedSections);\n    }\n  }\n  range.selectNodeContents(target);\n  target.focus();\n  selection.removeAllRanges();\n  selection.addRange(range);\n}"], "mappings": "AAAA,SAASA,gBAAgB,QAAQ,sBAAsB;AACvD,OAAO,SAASC,kBAAkBA,CAACC,UAAU,EAAE;EAC7C,MAAM;IACJC,OAAO;IACPC,UAAU;IACVC,aAAa,EAAE;MACb;MACAC,sBAAsB;MACtBC;IACF;EACF,CAAC,GAAGL,UAAU;EACd,IAAI,CAACE,UAAU,CAACI,OAAO,CAAC,CAAC,EAAE;IACzB;EACF;EACA,MAAMC,SAAS,GAAGC,QAAQ,CAACC,YAAY,CAAC,CAAC;EACzC,IAAI,CAACF,SAAS,EAAE;IACd;EACF;EACA,IAAIH,sBAAsB,IAAI,IAAI,EAAE;IAClC;IACA,IAAIG,SAAS,CAACG,UAAU,GAAG,CAAC,IAAIR,UAAU,CAACS,OAAO,CAAC,CAAC,CAACC,QAAQ,CAACL,SAAS,CAACM,UAAU,CAAC,CAAC,CAAC,CAACC,cAAc,CAAC,EAAE;MACrGP,SAAS,CAACQ,eAAe,CAAC,CAAC;IAC7B;IACA,IAAId,OAAO,EAAE;MACXC,UAAU,CAACS,OAAO,CAAC,CAAC,CAACK,IAAI,CAAC,CAAC;IAC7B;IACA;EACF;;EAEA;EACA,IAAI,CAACd,UAAU,CAACS,OAAO,CAAC,CAAC,CAACC,QAAQ,CAACd,gBAAgB,CAACU,QAAQ,CAAC,CAAC,EAAE;IAC9D;EACF;EACA,MAAMS,KAAK,GAAG,IAAIC,MAAM,CAACC,KAAK,CAAC,CAAC;EAChC,IAAIC,MAAM;EACV,IAAIhB,sBAAsB,KAAK,KAAK,EAAE;IACpCgB,MAAM,GAAGlB,UAAU,CAACS,OAAO,CAAC,CAAC;EAC/B,CAAC,MAAM;IACL,MAAMU,OAAO,GAAGhB,KAAK,CAACiB,QAAQ,CAAClB,sBAAsB,CAAC;IACtD,IAAIiB,OAAO,CAACE,IAAI,KAAK,OAAO,EAAE;MAC5BH,MAAM,GAAGlB,UAAU,CAACsB,mBAAmB,CAACpB,sBAAsB,CAAC;IACjE,CAAC,MAAM;MACLgB,MAAM,GAAGlB,UAAU,CAACuB,iBAAiB,CAACrB,sBAAsB,CAAC;IAC/D;EACF;EACAa,KAAK,CAACS,kBAAkB,CAACN,MAAM,CAAC;EAChCA,MAAM,CAACO,KAAK,CAAC,CAAC;EACdpB,SAAS,CAACQ,eAAe,CAAC,CAAC;EAC3BR,SAAS,CAACqB,QAAQ,CAACX,KAAK,CAAC;AAC3B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}