{"ast": null, "code": "export { default as CustomerDialog } from './CustomerDialog';\nexport { default as CustomerForm } from './CustomerForm';", "map": {"version": 3, "names": ["default", "CustomerDialog", "CustomerForm"], "sources": ["D:/HeThongCongTyQuanLyNhanCong/Microservice_With_Kubernetes/microservice_fe/src/components/customer/index.ts"], "sourcesContent": ["export { default as CustomerDialog } from './CustomerDialog';\nexport { default as CustomerForm } from './CustomerForm';\n"], "mappings": "AAAA,SAASA,OAAO,IAAIC,cAAc,QAAQ,kBAAkB;AAC5D,SAASD,OAAO,IAAIE,YAAY,QAAQ,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}