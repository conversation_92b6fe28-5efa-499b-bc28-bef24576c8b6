{"ast": null, "code": "import * as React from 'react';\nimport useEventCallback from '@mui/utils/useEventCallback';\nimport useId from '@mui/utils/useId';\nimport { useUtils } from \"../useUtils.js\";\nimport { usePickerTranslations } from \"../../../hooks/index.js\";\nimport { syncSelectionToDOM } from \"./syncSelectionToDOM.js\";\n/**\n * Generate the props to pass to the content element of each section of the field.\n * It is not used by the non-accessible DOM structure (with an <input /> element for editing).\n * It should be used in the MUI accessible DOM structure and the Base UI implementation.\n * @param {UseFieldRootPropsParameters} parameters The parameters of the hook.\n * @returns {UseFieldRootPropsReturnValue} The props to forward to the content element of each section of the field.\n */\nexport function useFieldSectionContentProps(parameters) {\n  const utils = useUtils();\n  const translations = usePickerTranslations();\n  const id = useId();\n  const {\n    focused,\n    domGetters,\n    stateResponse,\n    applyCharacterEditing,\n    manager: {\n      internal_fieldValueManager: fieldValueManager\n    },\n    stateResponse: {\n      // States and derived states\n      parsedSelectedSections,\n      sectionsValueBoundaries,\n      state,\n      value,\n      // Methods to update the states\n      clearActiveSection,\n      setCharacterQuery,\n      setSelectedSections,\n      updateSectionValue,\n      updateValueFromValueStr\n    },\n    internalPropsWithDefaults: {\n      disabled = false,\n      readOnly = false\n    }\n  } = parameters;\n  const isContainerEditable = parsedSelectedSections === 'all';\n  const isEditable = !isContainerEditable && !disabled && !readOnly;\n\n  /**\n   * If a section content has been updated with a value we don't want to keep,\n   * Then we need to imperatively revert it (we can't let React do it because the value did not change in his internal representation).\n   */\n  const revertDOMSectionChange = useEventCallback(sectionIndex => {\n    if (!domGetters.isReady()) {\n      return;\n    }\n    const section = state.sections[sectionIndex];\n    domGetters.getSectionContent(sectionIndex).innerHTML = section.value || section.placeholder;\n    syncSelectionToDOM({\n      focused,\n      domGetters,\n      stateResponse\n    });\n  });\n  const handleInput = useEventCallback(event => {\n    if (!domGetters.isReady()) {\n      return;\n    }\n    const target = event.target;\n    const keyPressed = target.textContent ?? '';\n    const sectionIndex = domGetters.getSectionIndexFromDOMElement(target);\n    const section = state.sections[sectionIndex];\n    if (readOnly) {\n      revertDOMSectionChange(sectionIndex);\n      return;\n    }\n    if (keyPressed.length === 0) {\n      if (section.value === '') {\n        revertDOMSectionChange(sectionIndex);\n        return;\n      }\n      const inputType = event.nativeEvent.inputType;\n      if (inputType === 'insertParagraph' || inputType === 'insertLineBreak') {\n        revertDOMSectionChange(sectionIndex);\n        return;\n      }\n      revertDOMSectionChange(sectionIndex);\n      clearActiveSection();\n      return;\n    }\n    applyCharacterEditing({\n      keyPressed,\n      sectionIndex\n    });\n\n    // The DOM value needs to remain the one React is expecting.\n    revertDOMSectionChange(sectionIndex);\n  });\n  const handleMouseUp = useEventCallback(event => {\n    // Without this, the browser will remove the selected when clicking inside an already-selected section.\n    event.preventDefault();\n  });\n  const handlePaste = useEventCallback(event => {\n    // prevent default to avoid the input `onInput` handler being called\n    event.preventDefault();\n    if (readOnly || disabled || typeof parsedSelectedSections !== 'number') {\n      return;\n    }\n    const activeSection = state.sections[parsedSelectedSections];\n    const pastedValue = event.clipboardData.getData('text');\n    const lettersOnly = /^[a-zA-Z]+$/.test(pastedValue);\n    const digitsOnly = /^[0-9]+$/.test(pastedValue);\n    const digitsAndLetterOnly = /^(([a-zA-Z]+)|)([0-9]+)(([a-zA-Z]+)|)$/.test(pastedValue);\n    const isValidPastedValue = activeSection.contentType === 'letter' && lettersOnly || activeSection.contentType === 'digit' && digitsOnly || activeSection.contentType === 'digit-with-letter' && digitsAndLetterOnly;\n    if (isValidPastedValue) {\n      setCharacterQuery(null);\n      updateSectionValue({\n        section: activeSection,\n        newSectionValue: pastedValue,\n        shouldGoToNextSection: true\n      });\n    }\n    // If the pasted value corresponds to a single section, but not the expected type, we skip the modification\n    else if (!lettersOnly && !digitsOnly) {\n      setCharacterQuery(null);\n      updateValueFromValueStr(pastedValue);\n    }\n  });\n  const handleDragOver = useEventCallback(event => {\n    event.preventDefault();\n    event.dataTransfer.dropEffect = 'none';\n  });\n  const createFocusHandler = React.useCallback(sectionIndex => () => {\n    if (disabled) {\n      return;\n    }\n    setSelectedSections(sectionIndex);\n  }, [disabled, setSelectedSections]);\n  return React.useCallback((section, sectionIndex) => {\n    const sectionBoundaries = sectionsValueBoundaries[section.type]({\n      currentDate: fieldValueManager.getDateFromSection(value, section),\n      contentType: section.contentType,\n      format: section.format\n    });\n    return {\n      // Event handlers\n      onInput: handleInput,\n      onPaste: handlePaste,\n      onMouseUp: handleMouseUp,\n      onDragOver: handleDragOver,\n      onFocus: createFocusHandler(sectionIndex),\n      // Aria attributes\n      'aria-labelledby': `${id}-${section.type}`,\n      'aria-readonly': readOnly,\n      'aria-valuenow': getSectionValueNow(section, utils),\n      'aria-valuemin': sectionBoundaries.minimum,\n      'aria-valuemax': sectionBoundaries.maximum,\n      'aria-valuetext': section.value ? getSectionValueText(section, utils) : translations.empty,\n      'aria-label': translations[section.type],\n      'aria-disabled': disabled,\n      // Other\n      tabIndex: isContainerEditable || sectionIndex > 0 ? -1 : 0,\n      contentEditable: !isContainerEditable && !disabled && !readOnly,\n      role: 'spinbutton',\n      id: `${id}-${section.type}`,\n      'data-range-position': section.dateName || undefined,\n      spellCheck: isEditable ? false : undefined,\n      autoCapitalize: isEditable ? 'off' : undefined,\n      autoCorrect: isEditable ? 'off' : undefined,\n      children: section.value || section.placeholder,\n      inputMode: section.contentType === 'letter' ? 'text' : 'numeric'\n    };\n  }, [sectionsValueBoundaries, id, isContainerEditable, disabled, readOnly, isEditable, translations, utils, handleInput, handlePaste, handleMouseUp, handleDragOver, createFocusHandler, fieldValueManager, value]);\n}\nfunction getSectionValueText(section, utils) {\n  if (!section.value) {\n    return undefined;\n  }\n  switch (section.type) {\n    case 'month':\n      {\n        if (section.contentType === 'digit') {\n          return utils.format(utils.setMonth(utils.date(), Number(section.value) - 1), 'month');\n        }\n        const parsedDate = utils.parse(section.value, section.format);\n        return parsedDate ? utils.format(parsedDate, 'month') : undefined;\n      }\n    case 'day':\n      return section.contentType === 'digit' ? utils.format(utils.setDate(utils.startOfYear(utils.date()), Number(section.value)), 'dayOfMonthFull') : section.value;\n    case 'weekDay':\n      // TODO: improve by providing the label of the week day\n      return undefined;\n    default:\n      return undefined;\n  }\n}\nfunction getSectionValueNow(section, utils) {\n  if (!section.value) {\n    return undefined;\n  }\n  switch (section.type) {\n    case 'weekDay':\n      {\n        if (section.contentType === 'letter') {\n          // TODO: improve by resolving the week day number from a letter week day\n          return undefined;\n        }\n        return Number(section.value);\n      }\n    case 'meridiem':\n      {\n        const parsedDate = utils.parse(`01:00 ${section.value}`, `${utils.formats.hours12h}:${utils.formats.minutes} ${section.format}`);\n        if (parsedDate) {\n          return utils.getHours(parsedDate) >= 12 ? 1 : 0;\n        }\n        return undefined;\n      }\n    case 'day':\n      return section.contentType === 'digit-with-letter' ? parseInt(section.value, 10) : Number(section.value);\n    case 'month':\n      {\n        if (section.contentType === 'digit') {\n          return Number(section.value);\n        }\n        const parsedDate = utils.parse(section.value, section.format);\n        return parsedDate ? utils.getMonth(parsedDate) + 1 : undefined;\n      }\n    default:\n      return section.contentType !== 'letter' ? Number(section.value) : undefined;\n  }\n}", "map": {"version": 3, "names": ["React", "useEventCallback", "useId", "useUtils", "usePickerTranslations", "syncSelectionToDOM", "useFieldSectionContentProps", "parameters", "utils", "translations", "id", "focused", "domGetters", "stateResponse", "applyCharacterEditing", "manager", "internal_fieldValueManager", "field<PERSON><PERSON>ueManager", "parsedSelectedSections", "sectionsValueBoundaries", "state", "value", "clearActiveSection", "setCharacterQuery", "setSelectedSections", "updateSectionValue", "updateValueFromValueStr", "internalPropsWithDefaults", "disabled", "readOnly", "isContainerEditable", "isEditable", "revertDOMSectionChange", "sectionIndex", "isReady", "section", "sections", "getSectionContent", "innerHTML", "placeholder", "handleInput", "event", "target", "keyPressed", "textContent", "getSectionIndexFromDOMElement", "length", "inputType", "nativeEvent", "handleMouseUp", "preventDefault", "handlePaste", "activeSection", "pastedValue", "clipboardData", "getData", "lettersOnly", "test", "digitsOnly", "digitsAndLetterOnly", "isValidPastedValue", "contentType", "newSectionValue", "shouldGoToNextSection", "handleDragOver", "dataTransfer", "dropEffect", "createFocusHandler", "useCallback", "sectionBoundaries", "type", "currentDate", "getDateFromSection", "format", "onInput", "onPaste", "onMouseUp", "onDragOver", "onFocus", "getSectionValueNow", "minimum", "maximum", "getSectionValueText", "empty", "tabIndex", "contentEditable", "role", "dateName", "undefined", "spell<PERSON>heck", "autoCapitalize", "autoCorrect", "children", "inputMode", "setMonth", "date", "Number", "parsedDate", "parse", "setDate", "startOfYear", "formats", "hours12h", "minutes", "getHours", "parseInt", "getMonth"], "sources": ["D:/HeThongCongTyQuanLyNhanCong/Microservice_With_Kubernetes/microservice_fe/node_modules/@mui/x-date-pickers/esm/internals/hooks/useField/useFieldSectionContentProps.js"], "sourcesContent": ["import * as React from 'react';\nimport useEventCallback from '@mui/utils/useEventCallback';\nimport useId from '@mui/utils/useId';\nimport { useUtils } from \"../useUtils.js\";\nimport { usePickerTranslations } from \"../../../hooks/index.js\";\nimport { syncSelectionToDOM } from \"./syncSelectionToDOM.js\";\n/**\n * Generate the props to pass to the content element of each section of the field.\n * It is not used by the non-accessible DOM structure (with an <input /> element for editing).\n * It should be used in the MUI accessible DOM structure and the Base UI implementation.\n * @param {UseFieldRootPropsParameters} parameters The parameters of the hook.\n * @returns {UseFieldRootPropsReturnValue} The props to forward to the content element of each section of the field.\n */\nexport function useFieldSectionContentProps(parameters) {\n  const utils = useUtils();\n  const translations = usePickerTranslations();\n  const id = useId();\n  const {\n    focused,\n    domGetters,\n    stateResponse,\n    applyCharacterEditing,\n    manager: {\n      internal_fieldValueManager: fieldValueManager\n    },\n    stateResponse: {\n      // States and derived states\n      parsedSelectedSections,\n      sectionsValueBoundaries,\n      state,\n      value,\n      // Methods to update the states\n      clearActiveSection,\n      setCharacterQuery,\n      setSelectedSections,\n      updateSectionValue,\n      updateValueFromValueStr\n    },\n    internalPropsWithDefaults: {\n      disabled = false,\n      readOnly = false\n    }\n  } = parameters;\n  const isContainerEditable = parsedSelectedSections === 'all';\n  const isEditable = !isContainerEditable && !disabled && !readOnly;\n\n  /**\n   * If a section content has been updated with a value we don't want to keep,\n   * Then we need to imperatively revert it (we can't let React do it because the value did not change in his internal representation).\n   */\n  const revertDOMSectionChange = useEventCallback(sectionIndex => {\n    if (!domGetters.isReady()) {\n      return;\n    }\n    const section = state.sections[sectionIndex];\n    domGetters.getSectionContent(sectionIndex).innerHTML = section.value || section.placeholder;\n    syncSelectionToDOM({\n      focused,\n      domGetters,\n      stateResponse\n    });\n  });\n  const handleInput = useEventCallback(event => {\n    if (!domGetters.isReady()) {\n      return;\n    }\n    const target = event.target;\n    const keyPressed = target.textContent ?? '';\n    const sectionIndex = domGetters.getSectionIndexFromDOMElement(target);\n    const section = state.sections[sectionIndex];\n    if (readOnly) {\n      revertDOMSectionChange(sectionIndex);\n      return;\n    }\n    if (keyPressed.length === 0) {\n      if (section.value === '') {\n        revertDOMSectionChange(sectionIndex);\n        return;\n      }\n      const inputType = event.nativeEvent.inputType;\n      if (inputType === 'insertParagraph' || inputType === 'insertLineBreak') {\n        revertDOMSectionChange(sectionIndex);\n        return;\n      }\n      revertDOMSectionChange(sectionIndex);\n      clearActiveSection();\n      return;\n    }\n    applyCharacterEditing({\n      keyPressed,\n      sectionIndex\n    });\n\n    // The DOM value needs to remain the one React is expecting.\n    revertDOMSectionChange(sectionIndex);\n  });\n  const handleMouseUp = useEventCallback(event => {\n    // Without this, the browser will remove the selected when clicking inside an already-selected section.\n    event.preventDefault();\n  });\n  const handlePaste = useEventCallback(event => {\n    // prevent default to avoid the input `onInput` handler being called\n    event.preventDefault();\n    if (readOnly || disabled || typeof parsedSelectedSections !== 'number') {\n      return;\n    }\n    const activeSection = state.sections[parsedSelectedSections];\n    const pastedValue = event.clipboardData.getData('text');\n    const lettersOnly = /^[a-zA-Z]+$/.test(pastedValue);\n    const digitsOnly = /^[0-9]+$/.test(pastedValue);\n    const digitsAndLetterOnly = /^(([a-zA-Z]+)|)([0-9]+)(([a-zA-Z]+)|)$/.test(pastedValue);\n    const isValidPastedValue = activeSection.contentType === 'letter' && lettersOnly || activeSection.contentType === 'digit' && digitsOnly || activeSection.contentType === 'digit-with-letter' && digitsAndLetterOnly;\n    if (isValidPastedValue) {\n      setCharacterQuery(null);\n      updateSectionValue({\n        section: activeSection,\n        newSectionValue: pastedValue,\n        shouldGoToNextSection: true\n      });\n    }\n    // If the pasted value corresponds to a single section, but not the expected type, we skip the modification\n    else if (!lettersOnly && !digitsOnly) {\n      setCharacterQuery(null);\n      updateValueFromValueStr(pastedValue);\n    }\n  });\n  const handleDragOver = useEventCallback(event => {\n    event.preventDefault();\n    event.dataTransfer.dropEffect = 'none';\n  });\n  const createFocusHandler = React.useCallback(sectionIndex => () => {\n    if (disabled) {\n      return;\n    }\n    setSelectedSections(sectionIndex);\n  }, [disabled, setSelectedSections]);\n  return React.useCallback((section, sectionIndex) => {\n    const sectionBoundaries = sectionsValueBoundaries[section.type]({\n      currentDate: fieldValueManager.getDateFromSection(value, section),\n      contentType: section.contentType,\n      format: section.format\n    });\n    return {\n      // Event handlers\n      onInput: handleInput,\n      onPaste: handlePaste,\n      onMouseUp: handleMouseUp,\n      onDragOver: handleDragOver,\n      onFocus: createFocusHandler(sectionIndex),\n      // Aria attributes\n      'aria-labelledby': `${id}-${section.type}`,\n      'aria-readonly': readOnly,\n      'aria-valuenow': getSectionValueNow(section, utils),\n      'aria-valuemin': sectionBoundaries.minimum,\n      'aria-valuemax': sectionBoundaries.maximum,\n      'aria-valuetext': section.value ? getSectionValueText(section, utils) : translations.empty,\n      'aria-label': translations[section.type],\n      'aria-disabled': disabled,\n      // Other\n      tabIndex: isContainerEditable || sectionIndex > 0 ? -1 : 0,\n      contentEditable: !isContainerEditable && !disabled && !readOnly,\n      role: 'spinbutton',\n      id: `${id}-${section.type}`,\n      'data-range-position': section.dateName || undefined,\n      spellCheck: isEditable ? false : undefined,\n      autoCapitalize: isEditable ? 'off' : undefined,\n      autoCorrect: isEditable ? 'off' : undefined,\n      children: section.value || section.placeholder,\n      inputMode: section.contentType === 'letter' ? 'text' : 'numeric'\n    };\n  }, [sectionsValueBoundaries, id, isContainerEditable, disabled, readOnly, isEditable, translations, utils, handleInput, handlePaste, handleMouseUp, handleDragOver, createFocusHandler, fieldValueManager, value]);\n}\nfunction getSectionValueText(section, utils) {\n  if (!section.value) {\n    return undefined;\n  }\n  switch (section.type) {\n    case 'month':\n      {\n        if (section.contentType === 'digit') {\n          return utils.format(utils.setMonth(utils.date(), Number(section.value) - 1), 'month');\n        }\n        const parsedDate = utils.parse(section.value, section.format);\n        return parsedDate ? utils.format(parsedDate, 'month') : undefined;\n      }\n    case 'day':\n      return section.contentType === 'digit' ? utils.format(utils.setDate(utils.startOfYear(utils.date()), Number(section.value)), 'dayOfMonthFull') : section.value;\n    case 'weekDay':\n      // TODO: improve by providing the label of the week day\n      return undefined;\n    default:\n      return undefined;\n  }\n}\nfunction getSectionValueNow(section, utils) {\n  if (!section.value) {\n    return undefined;\n  }\n  switch (section.type) {\n    case 'weekDay':\n      {\n        if (section.contentType === 'letter') {\n          // TODO: improve by resolving the week day number from a letter week day\n          return undefined;\n        }\n        return Number(section.value);\n      }\n    case 'meridiem':\n      {\n        const parsedDate = utils.parse(`01:00 ${section.value}`, `${utils.formats.hours12h}:${utils.formats.minutes} ${section.format}`);\n        if (parsedDate) {\n          return utils.getHours(parsedDate) >= 12 ? 1 : 0;\n        }\n        return undefined;\n      }\n    case 'day':\n      return section.contentType === 'digit-with-letter' ? parseInt(section.value, 10) : Number(section.value);\n    case 'month':\n      {\n        if (section.contentType === 'digit') {\n          return Number(section.value);\n        }\n        const parsedDate = utils.parse(section.value, section.format);\n        return parsedDate ? utils.getMonth(parsedDate) + 1 : undefined;\n      }\n    default:\n      return section.contentType !== 'letter' ? Number(section.value) : undefined;\n  }\n}"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,gBAAgB,MAAM,6BAA6B;AAC1D,OAAOC,KAAK,MAAM,kBAAkB;AACpC,SAASC,QAAQ,QAAQ,gBAAgB;AACzC,SAASC,qBAAqB,QAAQ,yBAAyB;AAC/D,SAASC,kBAAkB,QAAQ,yBAAyB;AAC5D;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,2BAA2BA,CAACC,UAAU,EAAE;EACtD,MAAMC,KAAK,GAAGL,QAAQ,CAAC,CAAC;EACxB,MAAMM,YAAY,GAAGL,qBAAqB,CAAC,CAAC;EAC5C,MAAMM,EAAE,GAAGR,KAAK,CAAC,CAAC;EAClB,MAAM;IACJS,OAAO;IACPC,UAAU;IACVC,aAAa;IACbC,qBAAqB;IACrBC,OAAO,EAAE;MACPC,0BAA0B,EAAEC;IAC9B,CAAC;IACDJ,aAAa,EAAE;MACb;MACAK,sBAAsB;MACtBC,uBAAuB;MACvBC,KAAK;MACLC,KAAK;MACL;MACAC,kBAAkB;MAClBC,iBAAiB;MACjBC,mBAAmB;MACnBC,kBAAkB;MAClBC;IACF,CAAC;IACDC,yBAAyB,EAAE;MACzBC,QAAQ,GAAG,KAAK;MAChBC,QAAQ,GAAG;IACb;EACF,CAAC,GAAGtB,UAAU;EACd,MAAMuB,mBAAmB,GAAGZ,sBAAsB,KAAK,KAAK;EAC5D,MAAMa,UAAU,GAAG,CAACD,mBAAmB,IAAI,CAACF,QAAQ,IAAI,CAACC,QAAQ;;EAEjE;AACF;AACA;AACA;EACE,MAAMG,sBAAsB,GAAG/B,gBAAgB,CAACgC,YAAY,IAAI;IAC9D,IAAI,CAACrB,UAAU,CAACsB,OAAO,CAAC,CAAC,EAAE;MACzB;IACF;IACA,MAAMC,OAAO,GAAGf,KAAK,CAACgB,QAAQ,CAACH,YAAY,CAAC;IAC5CrB,UAAU,CAACyB,iBAAiB,CAACJ,YAAY,CAAC,CAACK,SAAS,GAAGH,OAAO,CAACd,KAAK,IAAIc,OAAO,CAACI,WAAW;IAC3FlC,kBAAkB,CAAC;MACjBM,OAAO;MACPC,UAAU;MACVC;IACF,CAAC,CAAC;EACJ,CAAC,CAAC;EACF,MAAM2B,WAAW,GAAGvC,gBAAgB,CAACwC,KAAK,IAAI;IAC5C,IAAI,CAAC7B,UAAU,CAACsB,OAAO,CAAC,CAAC,EAAE;MACzB;IACF;IACA,MAAMQ,MAAM,GAAGD,KAAK,CAACC,MAAM;IAC3B,MAAMC,UAAU,GAAGD,MAAM,CAACE,WAAW,IAAI,EAAE;IAC3C,MAAMX,YAAY,GAAGrB,UAAU,CAACiC,6BAA6B,CAACH,MAAM,CAAC;IACrE,MAAMP,OAAO,GAAGf,KAAK,CAACgB,QAAQ,CAACH,YAAY,CAAC;IAC5C,IAAIJ,QAAQ,EAAE;MACZG,sBAAsB,CAACC,YAAY,CAAC;MACpC;IACF;IACA,IAAIU,UAAU,CAACG,MAAM,KAAK,CAAC,EAAE;MAC3B,IAAIX,OAAO,CAACd,KAAK,KAAK,EAAE,EAAE;QACxBW,sBAAsB,CAACC,YAAY,CAAC;QACpC;MACF;MACA,MAAMc,SAAS,GAAGN,KAAK,CAACO,WAAW,CAACD,SAAS;MAC7C,IAAIA,SAAS,KAAK,iBAAiB,IAAIA,SAAS,KAAK,iBAAiB,EAAE;QACtEf,sBAAsB,CAACC,YAAY,CAAC;QACpC;MACF;MACAD,sBAAsB,CAACC,YAAY,CAAC;MACpCX,kBAAkB,CAAC,CAAC;MACpB;IACF;IACAR,qBAAqB,CAAC;MACpB6B,UAAU;MACVV;IACF,CAAC,CAAC;;IAEF;IACAD,sBAAsB,CAACC,YAAY,CAAC;EACtC,CAAC,CAAC;EACF,MAAMgB,aAAa,GAAGhD,gBAAgB,CAACwC,KAAK,IAAI;IAC9C;IACAA,KAAK,CAACS,cAAc,CAAC,CAAC;EACxB,CAAC,CAAC;EACF,MAAMC,WAAW,GAAGlD,gBAAgB,CAACwC,KAAK,IAAI;IAC5C;IACAA,KAAK,CAACS,cAAc,CAAC,CAAC;IACtB,IAAIrB,QAAQ,IAAID,QAAQ,IAAI,OAAOV,sBAAsB,KAAK,QAAQ,EAAE;MACtE;IACF;IACA,MAAMkC,aAAa,GAAGhC,KAAK,CAACgB,QAAQ,CAAClB,sBAAsB,CAAC;IAC5D,MAAMmC,WAAW,GAAGZ,KAAK,CAACa,aAAa,CAACC,OAAO,CAAC,MAAM,CAAC;IACvD,MAAMC,WAAW,GAAG,aAAa,CAACC,IAAI,CAACJ,WAAW,CAAC;IACnD,MAAMK,UAAU,GAAG,UAAU,CAACD,IAAI,CAACJ,WAAW,CAAC;IAC/C,MAAMM,mBAAmB,GAAG,wCAAwC,CAACF,IAAI,CAACJ,WAAW,CAAC;IACtF,MAAMO,kBAAkB,GAAGR,aAAa,CAACS,WAAW,KAAK,QAAQ,IAAIL,WAAW,IAAIJ,aAAa,CAACS,WAAW,KAAK,OAAO,IAAIH,UAAU,IAAIN,aAAa,CAACS,WAAW,KAAK,mBAAmB,IAAIF,mBAAmB;IACnN,IAAIC,kBAAkB,EAAE;MACtBrC,iBAAiB,CAAC,IAAI,CAAC;MACvBE,kBAAkB,CAAC;QACjBU,OAAO,EAAEiB,aAAa;QACtBU,eAAe,EAAET,WAAW;QAC5BU,qBAAqB,EAAE;MACzB,CAAC,CAAC;IACJ;IACA;IAAA,KACK,IAAI,CAACP,WAAW,IAAI,CAACE,UAAU,EAAE;MACpCnC,iBAAiB,CAAC,IAAI,CAAC;MACvBG,uBAAuB,CAAC2B,WAAW,CAAC;IACtC;EACF,CAAC,CAAC;EACF,MAAMW,cAAc,GAAG/D,gBAAgB,CAACwC,KAAK,IAAI;IAC/CA,KAAK,CAACS,cAAc,CAAC,CAAC;IACtBT,KAAK,CAACwB,YAAY,CAACC,UAAU,GAAG,MAAM;EACxC,CAAC,CAAC;EACF,MAAMC,kBAAkB,GAAGnE,KAAK,CAACoE,WAAW,CAACnC,YAAY,IAAI,MAAM;IACjE,IAAIL,QAAQ,EAAE;MACZ;IACF;IACAJ,mBAAmB,CAACS,YAAY,CAAC;EACnC,CAAC,EAAE,CAACL,QAAQ,EAAEJ,mBAAmB,CAAC,CAAC;EACnC,OAAOxB,KAAK,CAACoE,WAAW,CAAC,CAACjC,OAAO,EAAEF,YAAY,KAAK;IAClD,MAAMoC,iBAAiB,GAAGlD,uBAAuB,CAACgB,OAAO,CAACmC,IAAI,CAAC,CAAC;MAC9DC,WAAW,EAAEtD,iBAAiB,CAACuD,kBAAkB,CAACnD,KAAK,EAAEc,OAAO,CAAC;MACjE0B,WAAW,EAAE1B,OAAO,CAAC0B,WAAW;MAChCY,MAAM,EAAEtC,OAAO,CAACsC;IAClB,CAAC,CAAC;IACF,OAAO;MACL;MACAC,OAAO,EAAElC,WAAW;MACpBmC,OAAO,EAAExB,WAAW;MACpByB,SAAS,EAAE3B,aAAa;MACxB4B,UAAU,EAAEb,cAAc;MAC1Bc,OAAO,EAAEX,kBAAkB,CAAClC,YAAY,CAAC;MACzC;MACA,iBAAiB,EAAE,GAAGvB,EAAE,IAAIyB,OAAO,CAACmC,IAAI,EAAE;MAC1C,eAAe,EAAEzC,QAAQ;MACzB,eAAe,EAAEkD,kBAAkB,CAAC5C,OAAO,EAAE3B,KAAK,CAAC;MACnD,eAAe,EAAE6D,iBAAiB,CAACW,OAAO;MAC1C,eAAe,EAAEX,iBAAiB,CAACY,OAAO;MAC1C,gBAAgB,EAAE9C,OAAO,CAACd,KAAK,GAAG6D,mBAAmB,CAAC/C,OAAO,EAAE3B,KAAK,CAAC,GAAGC,YAAY,CAAC0E,KAAK;MAC1F,YAAY,EAAE1E,YAAY,CAAC0B,OAAO,CAACmC,IAAI,CAAC;MACxC,eAAe,EAAE1C,QAAQ;MACzB;MACAwD,QAAQ,EAAEtD,mBAAmB,IAAIG,YAAY,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC;MAC1DoD,eAAe,EAAE,CAACvD,mBAAmB,IAAI,CAACF,QAAQ,IAAI,CAACC,QAAQ;MAC/DyD,IAAI,EAAE,YAAY;MAClB5E,EAAE,EAAE,GAAGA,EAAE,IAAIyB,OAAO,CAACmC,IAAI,EAAE;MAC3B,qBAAqB,EAAEnC,OAAO,CAACoD,QAAQ,IAAIC,SAAS;MACpDC,UAAU,EAAE1D,UAAU,GAAG,KAAK,GAAGyD,SAAS;MAC1CE,cAAc,EAAE3D,UAAU,GAAG,KAAK,GAAGyD,SAAS;MAC9CG,WAAW,EAAE5D,UAAU,GAAG,KAAK,GAAGyD,SAAS;MAC3CI,QAAQ,EAAEzD,OAAO,CAACd,KAAK,IAAIc,OAAO,CAACI,WAAW;MAC9CsD,SAAS,EAAE1D,OAAO,CAAC0B,WAAW,KAAK,QAAQ,GAAG,MAAM,GAAG;IACzD,CAAC;EACH,CAAC,EAAE,CAAC1C,uBAAuB,EAAET,EAAE,EAAEoB,mBAAmB,EAAEF,QAAQ,EAAEC,QAAQ,EAAEE,UAAU,EAAEtB,YAAY,EAAED,KAAK,EAAEgC,WAAW,EAAEW,WAAW,EAAEF,aAAa,EAAEe,cAAc,EAAEG,kBAAkB,EAAElD,iBAAiB,EAAEI,KAAK,CAAC,CAAC;AACpN;AACA,SAAS6D,mBAAmBA,CAAC/C,OAAO,EAAE3B,KAAK,EAAE;EAC3C,IAAI,CAAC2B,OAAO,CAACd,KAAK,EAAE;IAClB,OAAOmE,SAAS;EAClB;EACA,QAAQrD,OAAO,CAACmC,IAAI;IAClB,KAAK,OAAO;MACV;QACE,IAAInC,OAAO,CAAC0B,WAAW,KAAK,OAAO,EAAE;UACnC,OAAOrD,KAAK,CAACiE,MAAM,CAACjE,KAAK,CAACsF,QAAQ,CAACtF,KAAK,CAACuF,IAAI,CAAC,CAAC,EAAEC,MAAM,CAAC7D,OAAO,CAACd,KAAK,CAAC,GAAG,CAAC,CAAC,EAAE,OAAO,CAAC;QACvF;QACA,MAAM4E,UAAU,GAAGzF,KAAK,CAAC0F,KAAK,CAAC/D,OAAO,CAACd,KAAK,EAAEc,OAAO,CAACsC,MAAM,CAAC;QAC7D,OAAOwB,UAAU,GAAGzF,KAAK,CAACiE,MAAM,CAACwB,UAAU,EAAE,OAAO,CAAC,GAAGT,SAAS;MACnE;IACF,KAAK,KAAK;MACR,OAAOrD,OAAO,CAAC0B,WAAW,KAAK,OAAO,GAAGrD,KAAK,CAACiE,MAAM,CAACjE,KAAK,CAAC2F,OAAO,CAAC3F,KAAK,CAAC4F,WAAW,CAAC5F,KAAK,CAACuF,IAAI,CAAC,CAAC,CAAC,EAAEC,MAAM,CAAC7D,OAAO,CAACd,KAAK,CAAC,CAAC,EAAE,gBAAgB,CAAC,GAAGc,OAAO,CAACd,KAAK;IAChK,KAAK,SAAS;MACZ;MACA,OAAOmE,SAAS;IAClB;MACE,OAAOA,SAAS;EACpB;AACF;AACA,SAAST,kBAAkBA,CAAC5C,OAAO,EAAE3B,KAAK,EAAE;EAC1C,IAAI,CAAC2B,OAAO,CAACd,KAAK,EAAE;IAClB,OAAOmE,SAAS;EAClB;EACA,QAAQrD,OAAO,CAACmC,IAAI;IAClB,KAAK,SAAS;MACZ;QACE,IAAInC,OAAO,CAAC0B,WAAW,KAAK,QAAQ,EAAE;UACpC;UACA,OAAO2B,SAAS;QAClB;QACA,OAAOQ,MAAM,CAAC7D,OAAO,CAACd,KAAK,CAAC;MAC9B;IACF,KAAK,UAAU;MACb;QACE,MAAM4E,UAAU,GAAGzF,KAAK,CAAC0F,KAAK,CAAC,SAAS/D,OAAO,CAACd,KAAK,EAAE,EAAE,GAAGb,KAAK,CAAC6F,OAAO,CAACC,QAAQ,IAAI9F,KAAK,CAAC6F,OAAO,CAACE,OAAO,IAAIpE,OAAO,CAACsC,MAAM,EAAE,CAAC;QAChI,IAAIwB,UAAU,EAAE;UACd,OAAOzF,KAAK,CAACgG,QAAQ,CAACP,UAAU,CAAC,IAAI,EAAE,GAAG,CAAC,GAAG,CAAC;QACjD;QACA,OAAOT,SAAS;MAClB;IACF,KAAK,KAAK;MACR,OAAOrD,OAAO,CAAC0B,WAAW,KAAK,mBAAmB,GAAG4C,QAAQ,CAACtE,OAAO,CAACd,KAAK,EAAE,EAAE,CAAC,GAAG2E,MAAM,CAAC7D,OAAO,CAACd,KAAK,CAAC;IAC1G,KAAK,OAAO;MACV;QACE,IAAIc,OAAO,CAAC0B,WAAW,KAAK,OAAO,EAAE;UACnC,OAAOmC,MAAM,CAAC7D,OAAO,CAACd,KAAK,CAAC;QAC9B;QACA,MAAM4E,UAAU,GAAGzF,KAAK,CAAC0F,KAAK,CAAC/D,OAAO,CAACd,KAAK,EAAEc,OAAO,CAACsC,MAAM,CAAC;QAC7D,OAAOwB,UAAU,GAAGzF,KAAK,CAACkG,QAAQ,CAACT,UAAU,CAAC,GAAG,CAAC,GAAGT,SAAS;MAChE;IACF;MACE,OAAOrD,OAAO,CAAC0B,WAAW,KAAK,QAAQ,GAAGmC,MAAM,CAAC7D,OAAO,CAACd,KAAK,CAAC,GAAGmE,SAAS;EAC/E;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}