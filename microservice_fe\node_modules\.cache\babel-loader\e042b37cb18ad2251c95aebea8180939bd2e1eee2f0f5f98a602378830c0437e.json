{"ast": null, "code": "import { formatDistance } from \"./eu/_lib/formatDistance.js\";\nimport { formatLong } from \"./eu/_lib/formatLong.js\";\nimport { formatRelative } from \"./eu/_lib/formatRelative.js\";\nimport { localize } from \"./eu/_lib/localize.js\";\nimport { match } from \"./eu/_lib/match.js\";\n\n/**\n * @category Locales\n * @summary Basque locale.\n * @language Basque\n * @iso-639-2 eus\n * <AUTHOR> [@JacobSoderblom](https://github.com/JacobSoderblom)\n */\nexport const eu = {\n  code: \"eu\",\n  formatDistance: formatDistance,\n  formatLong: formatLong,\n  formatRelative: formatRelative,\n  localize: localize,\n  match: match,\n  options: {\n    weekStartsOn: 1 /* Monday */,\n    firstWeekContainsDate: 1\n  }\n};\n\n// Fallback for modularized imports:\nexport default eu;", "map": {"version": 3, "names": ["formatDistance", "formatLong", "formatRelative", "localize", "match", "eu", "code", "options", "weekStartsOn", "firstWeekContainsDate"], "sources": ["D:/HeThongCongTyQuanLyNhanCong/Microservice_With_Kubernetes/microservice_fe/node_modules/date-fns/locale/eu.js"], "sourcesContent": ["import { formatDistance } from \"./eu/_lib/formatDistance.js\";\nimport { formatLong } from \"./eu/_lib/formatLong.js\";\nimport { formatRelative } from \"./eu/_lib/formatRelative.js\";\nimport { localize } from \"./eu/_lib/localize.js\";\nimport { match } from \"./eu/_lib/match.js\";\n\n/**\n * @category Locales\n * @summary Basque locale.\n * @language Basque\n * @iso-639-2 eus\n * <AUTHOR> [@JacobSoderblom](https://github.com/JacobSoderblom)\n */\nexport const eu = {\n  code: \"eu\",\n  formatDistance: formatDistance,\n  formatLong: formatLong,\n  formatRelative: formatRelative,\n  localize: localize,\n  match: match,\n  options: {\n    weekStartsOn: 1 /* Monday */,\n    firstWeekContainsDate: 1,\n  },\n};\n\n// Fallback for modularized imports:\nexport default eu;\n"], "mappings": "AAAA,SAASA,cAAc,QAAQ,6BAA6B;AAC5D,SAASC,UAAU,QAAQ,yBAAyB;AACpD,SAASC,cAAc,QAAQ,6BAA6B;AAC5D,SAASC,QAAQ,QAAQ,uBAAuB;AAChD,SAASC,KAAK,QAAQ,oBAAoB;;AAE1C;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,EAAE,GAAG;EAChBC,IAAI,EAAE,IAAI;EACVN,cAAc,EAAEA,cAAc;EAC9BC,UAAU,EAAEA,UAAU;EACtBC,cAAc,EAAEA,cAAc;EAC9BC,QAAQ,EAAEA,QAAQ;EAClBC,KAAK,EAAEA,KAAK;EACZG,OAAO,EAAE;IACPC,YAAY,EAAE,CAAC,CAAC;IAChBC,qBAAqB,EAAE;EACzB;AACF,CAAC;;AAED;AACA,eAAeJ,EAAE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}